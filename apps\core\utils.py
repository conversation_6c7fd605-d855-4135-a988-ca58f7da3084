import random
# from twilio.rest import Client
import vonage
from django.conf import settings
from django.core.mail import send_mail
import jwt
import datetime
from redis import Redis


def generate_verification_code():
    return str(random.randint(100000, 999999))


# def send_otp(phone_number, otp):
#     client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
#     message = client.messages.create(
#         body=f'PickyPC verification OTP is: {otp}',
#         from_=settings.TWILIO_PHONE_NUMBER,
#         to=str(phone_number)
#     )
#     return message.sid

def send_sms(phone_number, text):
    client = vonage.Client(key=settings.VONAGE_API_KEY, secret=settings.VONAGE_API_SECRET)
    sms = vonage.Sms(client)

    response = sms.send_message({
        'from': settings.VONAGE_BRAND_NAME,  # PickyPC
        'to': str(phone_number),
        'text': f'{text}'
    })

    if response['messages'][0]['status'] == '0':
        return response['messages'][0]['message-id']
    else:
        raise Exception(f"Failed to send SMS: {response['messages'][0]['error-text']}")


def send_email(email, subject, message):
    subject = f'{subject}'
    message = f'{message}'
    from_email = settings.DEFAULT_FROM_EMAIL
    recipient_list = [email]

    send_mail(subject, message, from_email, recipient_list)




