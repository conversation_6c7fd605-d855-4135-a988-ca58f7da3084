from rest_framework.permissions import BasePermission
from apps.staff.authorization.permissions import CanAccessStaffAPI
from apps.staff.common.constants import STAFF_GROUPS


class CanManagePayments(BasePermission):
    """
    Permission class for payment management operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        # Check basic staff API access
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check specific payment permissions based on action
        if hasattr(view, 'action'):
            action = view.action
            
            if action in ['list', 'retrieve']:
                return request.user.has_perm('payments.view_paymentoption')
            elif action in ['create', 'update', 'partial_update']:
                return request.user.has_perm('payments.manage_payment_options')
            elif action == 'destroy':
                return request.user.has_perm('payments.delete_paymentoption')
            elif action == 'analytics':
                return request.user.has_perm('payments.payment_analytics')
            elif action in ['transactions', 'monitoring']:
                return request.user.has_perm('payments.payment_monitoring')
        
        return request.user.has_perm('payments.view_paymentoption')


class CanViewPaymentAnalytics(BasePermission):
    """
    Permission class for payment analytics operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check if user has analytics permissions
        user_groups = set(request.user.groups.values_list('name', flat=True))
        allowed_groups = {
            STAFF_GROUPS['FINANCE_MANAGER'],
            STAFF_GROUPS['SYSTEM_ADMINISTRATOR'],
            STAFF_GROUPS['ORDER_MANAGER']
        }
        
        return (
            request.user.is_superuser or
            bool(user_groups.intersection(allowed_groups)) or
            request.user.has_perm('payments.payment_analytics')
        )


class CanMonitorPayments(BasePermission):
    """
    Permission class for payment monitoring operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check if user has monitoring permissions
        user_groups = set(request.user.groups.values_list('name', flat=True))
        allowed_groups = {
            STAFF_GROUPS['FINANCE_MANAGER'],
            STAFF_GROUPS['CUSTOMER_SERVICE_MANAGER'],
            STAFF_GROUPS['CUSTOMER_SERVICE_REPRESENTATIVE'],
            STAFF_GROUPS['SYSTEM_ADMINISTRATOR']
        }
        
        return (
            request.user.is_superuser or
            bool(user_groups.intersection(allowed_groups)) or
            request.user.has_perm('payments.payment_monitoring')
        )


class CanManageDisputes(BasePermission):
    """
    Permission class for payment dispute management
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check specific dispute permissions based on action
        if hasattr(view, 'action'):
            action = view.action
            
            if action in ['list', 'retrieve']:
                return request.user.has_perm('payments.view_paymentdispute')
            elif action in ['update', 'partial_update']:
                return request.user.has_perm('payments.change_paymentdispute')
            elif action in ['assign', 'resolve']:
                return request.user.has_perm('payments.manage_paypal_disputes')
        
        return request.user.has_perm('payments.view_paymentdispute')


class CanAccessPayPalData(BasePermission):
    """
    Permission class for PayPal transaction access
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check if user has PayPal access permissions
        user_groups = set(request.user.groups.values_list('name', flat=True))
        allowed_groups = {
            STAFF_GROUPS['FINANCE_MANAGER'],
            STAFF_GROUPS['CUSTOMER_SERVICE_MANAGER'],
            STAFF_GROUPS['SYSTEM_ADMINISTRATOR']
        }
        
        return (
            request.user.is_superuser or
            bool(user_groups.intersection(allowed_groups)) or
            request.user.has_perm('payments.view_paypal_transactions')
        )


class IsFinanceStaff(BasePermission):
    """
    Permission class for finance staff
    Limited to finance-related operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        return (
            request.user.is_superuser or
            request.user.groups.filter(name__in=[
                'Finance Manager (FM)',
                'Finance Team Member (FTM)'
            ]).exists()
        )
    
    def has_object_permission(self, request, view, obj):
        # Finance staff can access payment data for analysis
        if hasattr(view, 'action') and view.action in ['retrieve', 'update', 'partial_update']:
            return True
        return self.has_permission(request, view)
