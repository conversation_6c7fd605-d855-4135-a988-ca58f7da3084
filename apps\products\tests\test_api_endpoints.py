# Test API endpoints - CRUD operations, permissions, status codes
import pytest
from decimal import Decimal
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from model_bakery import baker
from apps.products.models import (
    Category, Product, ProductVariant, Review, ProductType, Brand, Attribute, AttributeValue
)
from apps.customers.models import Customer

User = get_user_model()


@pytest.fixture
def admin_user():
    """Create admin user for testing"""
    return baker.make(User, is_staff=True, is_superuser=True)


@pytest.fixture
def regular_user():
    """Create regular user for testing"""
    return baker.make(User, is_staff=False)


@pytest.fixture
def authenticated_client(admin_user):
    """Create authenticated API client"""
    client = APIClient()
    client.force_authenticate(user=admin_user)
    return client


@pytest.fixture
def anonymous_client():
    """Create anonymous API client"""
    return APIClient()


@pytest.mark.django_db
class TestCategoryViewSet:
    """Test Category API endpoints"""
    
    def test_list_categories_anonymous_user(self, anonymous_client):
        """Test anonymous users can list categories"""
        baker.make(Category, _quantity=3)
        
        response = anonymous_client.get('/api/products/categories/')
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 3
    
    def test_retrieve_category_anonymous_user(self, anonymous_client):
        """Test anonymous users can retrieve a category"""
        category = baker.make(Category, title="Electronics")
        
        response = anonymous_client.get(f'/api/products/categories/{category.id}/')
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['title'] == "Electronics"
    
    def test_create_category_anonymous_user_returns_401(self, anonymous_client):
        """Test anonymous users cannot create categories"""
        category_data = {'title': 'New Category'}
        
        response = anonymous_client.post('/api/products/categories/', category_data)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_create_category_admin_user_success(self, authenticated_client):
        """Test admin users can create categories"""
        category_data = {'title': 'New Category'}
        
        response = authenticated_client.post('/api/products/categories/', category_data)
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['title'] == 'New Category'
        assert response.data['slug'] == 'new-category'
    
    def test_create_category_with_parent(self, authenticated_client):
        """Test creating category with parent"""
        parent = baker.make(Category, title="Electronics", slug="electronics")
        category_data = {
            'title': 'Laptops',
            'parent': parent.id
        }
        
        response = authenticated_client.post('/api/products/categories/', category_data)
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['parent'] == parent.id
    
    def test_update_category_admin_user(self, authenticated_client):
        """Test admin users can update categories"""
        category = baker.make(Category, title="Old Title")
        update_data = {'title': 'Updated Title'}
        
        response = authenticated_client.patch(f'/api/products/categories/{category.id}/', update_data)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['title'] == 'Updated Title'
    
    def test_delete_category_admin_user(self, authenticated_client):
        """Test admin users can delete categories"""
        category = baker.make(Category)
        
        response = authenticated_client.delete(f'/api/products/categories/{category.id}/')
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert not Category.objects.filter(id=category.id).exists()
    
    def test_create_category_validation_error(self, authenticated_client):
        """Test category creation with validation errors"""
        # Test with empty title
        response = authenticated_client.post('/api/products/categories/', {'title': ''})
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'title' in response.data
    
    def test_create_category_duplicate_slug_same_parent(self, authenticated_client):
        """Test creating category with duplicate slug under same parent"""
        parent = baker.make(Category)
        baker.make(Category, title="Test Category", parent=parent, slug="test-category")
        
        # Try to create another category with same title under same parent
        response = authenticated_client.post('/api/products/categories/', {
            'title': 'Test Category',
            'parent': parent.id
        })
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.django_db
class TestProductViewSet:
    """Test Product API endpoints"""
    
    def test_list_products_anonymous_user(self, anonymous_client):
        """Test anonymous users can list products"""
        # Create products with variants for proper serialization
        product = baker.make(Product)
        baker.make(ProductVariant, product=product, is_active=True)
        
        response = anonymous_client.get('/api/products/')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'results' in response.data  # Paginated response
    
    def test_retrieve_product_by_slug(self, anonymous_client):
        """Test retrieving product by slug"""
        product = baker.make(Product, slug="gaming-laptop")
        baker.make(ProductVariant, product=product, is_active=True)
        
        response = anonymous_client.get(f'/api/products/{product.slug}/')
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['slug'] == "gaming-laptop"
    
    def test_list_products_by_category_slug(self, anonymous_client):
        """Test listing products by category slug"""
        category = baker.make(Category, slug="laptops")
        product = baker.make(Product, category=category)
        baker.make(ProductVariant, product=product, is_active=True)
        
        response = anonymous_client.get(f'/api/products/category/{category.slug}/')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'results' in response.data
    
    def test_list_products_by_category_with_children(self, anonymous_client):
        """Test listing products includes children categories"""
        parent_category = baker.make(Category, slug="electronics")
        child_category = baker.make(Category, slug="laptops", parent=parent_category)
        
        # Product in parent category
        product1 = baker.make(Product, category=parent_category)
        baker.make(ProductVariant, product=product1, is_active=True)
        
        # Product in child category
        product2 = baker.make(Product, category=child_category)
        baker.make(ProductVariant, product=product2, is_active=True)
        
        response = anonymous_client.get(f'/api/products/category/{parent_category.slug}/')
        
        assert response.status_code == status.HTTP_200_OK
        # Should include products from both parent and child categories
        assert len(response.data['results']) == 2
    
    def test_product_search_filter(self, anonymous_client):
        """Test product search functionality"""
        product1 = baker.make(Product, title="Gaming Laptop")
        product2 = baker.make(Product, title="Office Desktop")
        baker.make(ProductVariant, product=product1, is_active=True)
        baker.make(ProductVariant, product=product2, is_active=True)
        
        response = anonymous_client.get('/api/products/?search=Gaming')
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 1
        assert response.data['results'][0]['title'] == "Gaming Laptop"
    
    def test_product_price_range_filter(self, anonymous_client):
        """Test product price range filtering"""
        product1 = baker.make(Product)
        product2 = baker.make(Product)
        baker.make(ProductVariant, product=product1, price=Decimal('500.00'), is_active=True)
        baker.make(ProductVariant, product=product2, price=Decimal('1500.00'), is_active=True)
        
        response = anonymous_client.get('/api/products/?min_price=400&max_price=600')
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 1
    
    def test_product_brand_filter(self, anonymous_client):
        """Test product brand filtering"""
        brand = baker.make(Brand, slug="apple")
        product1 = baker.make(Product, brand=brand)
        product2 = baker.make(Product)  # Different brand
        baker.make(ProductVariant, product=product1, is_active=True)
        baker.make(ProductVariant, product=product2, is_active=True)
        
        response = anonymous_client.get('/api/products/?brand=apple')
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 1
    
    def test_product_condition_filter(self, anonymous_client):
        """Test product condition filtering"""
        product1 = baker.make(Product)
        product2 = baker.make(Product)
        baker.make(ProductVariant, product=product1, condition='New', is_active=True)
        baker.make(ProductVariant, product=product2, condition='Used', is_active=True)
        
        response = anonymous_client.get('/api/products/?condition=New')
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 1
    
    def test_product_sorting_by_title(self, anonymous_client):
        """Test product sorting by title"""
        product1 = baker.make(Product, title="B Product")
        product2 = baker.make(Product, title="A Product")
        baker.make(ProductVariant, product=product1, is_active=True)
        baker.make(ProductVariant, product=product2, is_active=True)
        
        response = anonymous_client.get('/api/products/?sort_by=title_asc')
        
        assert response.status_code == status.HTTP_200_OK
        results = response.data['results']
        assert results[0]['title'] == "A Product"
        assert results[1]['title'] == "B Product"
    
    def test_product_sorting_by_price(self, anonymous_client):
        """Test product sorting by price"""
        product1 = baker.make(Product)
        product2 = baker.make(Product)
        baker.make(ProductVariant, product=product1, price=Decimal('1000.00'), is_active=True)
        baker.make(ProductVariant, product=product2, price=Decimal('500.00'), is_active=True)
        
        response = anonymous_client.get('/api/products/?sort_by=price_asc')
        
        assert response.status_code == status.HTTP_200_OK
        # Should be sorted by minimum price
        results = response.data['results']
        assert len(results) == 2
    
    def test_product_not_found(self, anonymous_client):
        """Test retrieving non-existent product"""
        response = anonymous_client.get('/api/products/non-existent-slug/')
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_product_create_not_allowed(self, authenticated_client):
        """Test that product creation is not allowed via API"""
        product_data = {'title': 'New Product'}
        
        response = authenticated_client.post('/api/products/', product_data)
        
        # Should return 405 Method Not Allowed since only GET is allowed
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED


@pytest.mark.django_db
class TestProductFilterOptionsViewSet:
    """Test Product Filter Options API endpoints"""
    
    def test_filter_options_without_product_type(self, anonymous_client):
        """Test getting filter options without specifying product type"""
        # Create some test data
        brand = baker.make(Brand, slug="test-brand", title="Test Brand")
        product = baker.make(Product, brand=brand)
        baker.make(ProductVariant, product=product, price=Decimal('100.00'), is_active=True)
        baker.make(ProductVariant, product=product, price=Decimal('200.00'), is_active=True)
        
        response = anonymous_client.get('/api/products/product-filter-options/')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'price_range' in response.data
        assert 'condition' in response.data
        assert 'brands' in response.data
        assert response.data['price_range']['min'] == 100.00
        assert response.data['price_range']['max'] == 200.00
    
    def test_filter_options_with_product_type(self, anonymous_client):
        """Test getting filter options for specific product type"""
        product_type = baker.make(ProductType)
        brand = baker.make(Brand)
        
        # Create BrandProductType relationship
        from apps.products.models import BrandProductType
        baker.make(BrandProductType, brand=brand, product_type=product_type)
        
        product = baker.make(Product, product_type=product_type, brand=brand)
        baker.make(ProductVariant, product=product, price=Decimal('150.00'), is_active=True)
        
        response = anonymous_client.get(f'/api/products/product-filter-options/?product_type_id={product_type.id}')
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['price_range']['min'] == 150.00
        assert len(response.data['brands']) >= 1


@pytest.mark.django_db
class TestProductTypeAttributeValueViewSet:
    """Test Product Type Attribute Value API endpoints"""

    def test_get_attribute_values_for_product_type(self, anonymous_client):
        """Test getting attribute values for a product type"""
        product_type = baker.make(ProductType)
        attribute = baker.make(Attribute, title="Color")
        attr_value = baker.make(AttributeValue, attribute=attribute, attribute_value="Red", for_filtering=True)

        # Create ProductTypeAttribute relationship
        from apps.products.models import ProductTypeAttribute
        baker.make(ProductTypeAttribute, product_type=product_type, attribute=attribute)

        response = anonymous_client.get(f'/api/products/product-types/?product_type_id={product_type.id}')

        assert response.status_code == status.HTTP_200_OK
        assert 'Color' in response.data
        assert 'Red' in response.data['Color']

    def test_get_attribute_values_invalid_product_type(self, anonymous_client):
        """Test getting attribute values with invalid product type ID"""
        response = anonymous_client.get('/api/products/product-types/?product_type_id=invalid')

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data

    def test_get_attribute_values_missing_product_type(self, anonymous_client):
        """Test getting attribute values without product type ID"""
        response = anonymous_client.get('/api/products/product-types/')

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'error' in response.data


@pytest.mark.django_db
class TestReviewViewSet:
    """Test Review API endpoints (nested under products)"""

    def test_list_product_reviews(self, anonymous_client):
        """Test listing reviews for a specific product"""
        product = baker.make(Product, slug="test-product")
        customer = baker.make(Customer)
        baker.make(Review, product=product, customer=customer, title="Great product", rating=5)
        baker.make(Review, product=product, customer=customer, title="Good product", rating=4)

        response = anonymous_client.get(f'/api/products/{product.slug}/reviews/')

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 2

    def test_create_product_review(self, authenticated_client):
        """Test creating a review for a product"""
        product = baker.make(Product, slug="test-product")
        customer = baker.make(Customer)

        review_data = {
            'title': 'Excellent product',
            'description': 'Really love this product!',
            'rating': 5,
            'customer': customer.id
        }

        response = authenticated_client.post(f'/api/products/{product.slug}/reviews/', review_data)

        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['title'] == 'Excellent product'
        assert response.data['rating'] == 5

    def test_create_review_updates_product_rating(self, authenticated_client):
        """Test that creating a review updates the product's average rating"""
        product = baker.make(Product, slug="test-product", average_rating=0.0)
        customer = baker.make(Customer)

        review_data = {
            'title': 'Great product',
            'description': 'Love it!',
            'rating': 4,
            'customer': customer.id
        }

        response = authenticated_client.post(f'/api/products/{product.slug}/reviews/', review_data)

        assert response.status_code == status.HTTP_201_CREATED

        # Check that product's average rating was updated
        product.refresh_from_db()
        assert product.average_rating == 4.0

    def test_retrieve_specific_review(self, anonymous_client):
        """Test retrieving a specific review"""
        product = baker.make(Product, slug="test-product")
        customer = baker.make(Customer)
        review = baker.make(Review, product=product, customer=customer, title="Test Review")

        response = anonymous_client.get(f'/api/products/{product.slug}/reviews/{review.id}/')

        assert response.status_code == status.HTTP_200_OK
        assert response.data['title'] == "Test Review"

    def test_review_rating_validation(self, authenticated_client):
        """Test review rating validation"""
        product = baker.make(Product, slug="test-product")
        customer = baker.make(Customer)

        # Test invalid rating (too high)
        review_data = {
            'title': 'Invalid review',
            'description': 'Test',
            'rating': 6,  # Invalid - max is 5
            'customer': customer.id
        }

        response = authenticated_client.post(f'/api/products/{product.slug}/reviews/', review_data)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'rating' in response.data

    def test_review_for_nonexistent_product(self, authenticated_client):
        """Test creating review for non-existent product"""
        customer = baker.make(Customer)

        review_data = {
            'title': 'Review for nothing',
            'description': 'Test',
            'rating': 5,
            'customer': customer.id
        }

        response = authenticated_client.post('/api/products/nonexistent-slug/reviews/', review_data)

        assert response.status_code == status.HTTP_404_NOT_FOUND
