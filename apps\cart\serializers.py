from django.conf import settings
from django.core.exceptions import ValidationError
from rest_framework.serializers import ModelSerializer
from rest_framework import serializers
from django.db.models import Sum, F
from decimal import Decimal
from .models import Cart, CartItem
from apps.products.serializers import SimpleProductSerializer, SimpleProductVariantSerializer
from apps.products.models import ProductVariant, Product
from utils.shipping_cost import calculate_shipping_cost


class CartItemSerializer(ModelSerializer):
    product = SimpleProductSerializer()
    product_variant = SimpleProductVariantSerializer()
    qty_price = serializers.SerializerMethodField()
    extra_data = serializers.JSONField()

    # @extend_schema_field(Decimal)
    # def get_qty_price(self, cart_item: CartItem) -> Decimal:
    #     # qty_price = cart_item.quantity * cart_item.product_variant.price
    #     # return Decimal(qty_price)
    #     return cart_item.get_total_item_price()

    def get_qty_price(self, cart_item):
        return cart_item.get_total_item_price()

    class Meta:
        model = CartItem
        fields = ['id', 'product', 'product_variant', 'quantity', 'qty_price', 'extra_data',
                  'is_selected']


class CartSerializer(ModelSerializer):
    """Serializer for cart without shipping calculations - shows only item totals"""
    id = serializers.UUIDField(read_only=True)
    cart_items = CartItemSerializer(many=True, read_only=True)
    # total_price = serializers.SerializerMethodField()
    # cart_weight = serializers.SerializerMethodField()
    # item_count = serializers.SerializerMethodField()

    # Selection-related fields
    selected_total_price = serializers.SerializerMethodField()
    selected_cart_weight = serializers.SerializerMethodField()
    selected_item_count = serializers.SerializerMethodField()
    all_items_selected = serializers.SerializerMethodField()

    # def get_cart_weight(self, cart):
    #     # Use prefetched cart_items
    #     return sum(item.quantity * item.product_variant.weight for item in cart.cart_items.all())

    # def get_total_price(self, cart):
    #     # Use prefetched cart_items with discounted prices
    #     return sum(item.get_total_item_price() for item in cart.cart_items.all())

    # def get_item_count(self, cart):
    #     """Get total number of items in cart"""
    #     return sum(item.quantity for item in cart.cart_items.all())

    def get_selected_total_price(self, cart):
        """Get total price of selected items only"""
        return sum(item.get_total_item_price() for item in cart.cart_items.filter(is_selected=True))

    def get_selected_cart_weight(self, cart):
        """Get total weight of selected items only"""
        return sum(item.quantity * item.product_variant.weight for item in cart.cart_items.filter(is_selected=True))

    def get_selected_item_count(self, cart):
        """Get count of selected items only"""
        return sum(item.quantity for item in cart.cart_items.filter(is_selected=True))

    def get_all_items_selected(self, cart):
        """Check if all items are selected"""
        return cart.get_all_items_selected()

    class Meta:
        model = Cart
        fields = ['id', 'cart_items', 'customer', 'selected_total_price', 'selected_cart_weight',
                  'selected_item_count', 'all_items_selected']


class CartWithShippingSerializer(ModelSerializer):
    """Serializer for cart with shipping calculations - used after shipping calculation"""
    id = serializers.UUIDField(read_only=True)
    cart_items = CartItemSerializer(many=True, read_only=True)
    total_price = serializers.SerializerMethodField()
    shipping_cost = serializers.SerializerMethodField()
    packing_cost = serializers.SerializerMethodField()
    total_volume = serializers.SerializerMethodField()
    grand_total = serializers.SerializerMethodField()
    cart_weight = serializers.SerializerMethodField()
    item_count = serializers.SerializerMethodField()

    # Selection-related fields with shipping
    selected_total_price = serializers.SerializerMethodField()
    selected_cart_weight = serializers.SerializerMethodField()
    selected_item_count = serializers.SerializerMethodField()
    selected_shipping_cost = serializers.SerializerMethodField()
    selected_grand_total = serializers.SerializerMethodField()
    all_items_selected = serializers.SerializerMethodField()

    def get_cart_weight(self, cart):
        # Use prefetched cart_items
        return sum(item.quantity * item.product_variant.weight for item in cart.cart_items.all())

    def get_total_price(self, cart):
        # Use prefetched cart_items with discounted prices
        return sum(item.get_total_item_price() for item in cart.cart_items.all())

    def get_shipping_cost(self, cart):
        # Use the cart's calculated shipping_cost field
        return getattr(cart, 'shipping_cost', Decimal('0.00'))

    def get_grand_total(self, cart):
        return self.get_total_price(cart) + self.get_shipping_cost(cart) + self.get_packing_cost(cart)

    def get_packing_cost(self, cart):
        """Get packing cost from cart"""
        return getattr(cart, 'packing_cost', Decimal('0.00'))

    def get_total_volume(self, cart):
        """Get total volume from cart"""
        return getattr(cart, 'total_volume', Decimal('0.0000'))

    def get_item_count(self, cart):
        """Get total number of items in cart"""
        return sum(item.quantity for item in cart.cart_items.all())

    def get_selected_total_price(self, cart):
        """Get total price of selected items only"""
        return sum(item.get_total_item_price() for item in cart.cart_items.filter(is_selected=True))

    def get_selected_cart_weight(self, cart):
        """Get total weight of selected items only"""
        return sum(item.quantity * item.product_variant.weight for item in cart.cart_items.filter(is_selected=True))

    def get_selected_item_count(self, cart):
        """Get count of selected items only"""
        return sum(item.quantity for item in cart.cart_items.filter(is_selected=True))

    def get_selected_shipping_cost(self, cart):
        """Get shipping cost for selected items (same as total for now)"""
        # For now, return the same shipping cost since shipping is calculated for the whole cart
        # In future, this could be proportional based on weight/volume of selected items
        return cart.shipping_cost

    def get_selected_grand_total(self, cart):
        """Get grand total for selected items only"""
        selected_total = self.get_selected_total_price(cart)
        selected_shipping = self.get_selected_shipping_cost(cart)
        return selected_total + selected_shipping + cart.packing_cost

    def get_all_items_selected(self, cart):
        """Check if all items are selected"""
        return cart.get_all_items_selected()

    class Meta:
        model = Cart
        fields = ['id', 'cart_items', 'customer', 'cart_weight', 'total_price', 'shipping_cost',
                  'packing_cost', 'total_volume',
                  # 'packing_details',
                  'grand_total', 'item_count',
                  'selected_total_price', 'selected_cart_weight', 'selected_item_count',
                  'selected_shipping_cost', 'selected_grand_total', 'all_items_selected',
                  'last_shipping_calculation', 'created_at']


class AddCartItemSerializer(ModelSerializer):
    product_id = serializers.IntegerField()
    # This querying is just for validation
    product_variant = serializers.PrimaryKeyRelatedField(queryset=ProductVariant.objects.all())
    extra_data = serializers.JSONField(default={})

    # Validating the product_id(pk) field of the passed object to the serializer
    def validate_product_id(self, value):
        if not Product.objects.filter(pk=value).exists():
            raise serializers.ValidationError('No product with given ID was found')
        return value

    # Check cart weight before adding more items
    def validate(self, data):
        cart_id = self.context['cart_id']
        product_variant = data['product_variant']
        quantity = data['quantity']

        cart = Cart.objects.get(id=cart_id)
        current_weight = cart.get_cart_weight()
        additional_weight = product_variant.weight * quantity

        if current_weight + additional_weight > settings.MAX_CART_WEIGHT:
            raise ValidationError(
                f"Adding this item would exceed the maximum weight limit of {settings.MAX_CART_WEIGHT} grams.")

        return data

    # Validating the product_variant_id(pk) field of the passed object to the serializer
    # def validate_product_variant_id(self, value):
    #     if not ProductVariant.objects.filter(pk=value).exists():
    #         raise serializers.ValidationError('No product variant with given ID was found')
    #     return value

    # If item exist in the cart increase the quantity or create a cart and add the item
    def save(self, **kwargs):
        cart_id = self.context['cart_id']
        product_id = self.validated_data['product_id']
        product_variant = self.validated_data['product_variant']
        quantity = self.validated_data['quantity']
        extra_data = self.validated_data['extra_data']

        # Calculate the price to use (discounted or regular)
        price_to_use = product_variant.get_discounted_price() or product_variant.price

        # Separate Query and Command (but this isn't working)
        # Client get a 404 error when try to get a cart that doesn't exist on the server
        try:
            cart = Cart.objects.get(id=cart_id)  # Query
        except Cart.DoesNotExist:
            cart = Cart.objects.create(id=cart_id)  # Command

        # Update quantity if the item exists in the cart
        try:
            cart_item = CartItem.objects.get(
                cart=cart,
                product_id=product_id,
                product_variant=product_variant
            )
            cart_item.quantity += quantity
            # cart_item.price = price_to_use  # Update price in case it has changed
            cart_item.save()
            self.instance = cart_item
        except CartItem.DoesNotExist:
            # Create a new cart item
            self.instance = CartItem.objects.create(
                cart=cart,
                product_id=product_id,
                product_variant=product_variant,
                quantity=quantity,
                # price=price_to_use,  # Set price at creation
                extra_data=extra_data
            )

        # Note: Shipping calculation is now decoupled and done on-demand

    class Meta:
        model = CartItem
        fields = ['id', 'product_id', 'product_variant', 'quantity', 'extra_data']


class UpdateCartItemSerializer(ModelSerializer):
    quantity = serializers.IntegerField()

    def validate_quantity(self, value):
        if value < 1:
            raise serializers.ValidationError("Quantity must be at least 1.")
        cart_item = self.instance
        cart = cart_item.cart
        product_variant = cart_item.product_variant

        # Calculate the weight difference
        weight_difference = (value - cart_item.quantity) * product_variant.weight

        # Check if the new total weight would exceed the limit
        if cart.get_cart_weight() + weight_difference > settings.MAX_CART_WEIGHT:
            raise serializers.ValidationError(
                f"Updating this item would exceed the maximum weight limit of {settings.MAX_CART_WEIGHT} grams."
            )

        return value

    class Meta:
        model = CartItem
        fields = ['quantity']


class UpdateCartSerializer(ModelSerializer):
    """Serializer for updating cart customer assignment"""

    class Meta:
        model = Cart
        fields = ['customer']
        read_only_fields = ['customer']  # Customer is set automatically in the view

    def validate(self, data):
        """Validate that cart can be updated"""
        cart = self.instance

        if cart and cart.customer is not None:
            raise serializers.ValidationError(
                "Cart is already assigned to a customer and cannot be updated"
            )

        return data


class ShippingCalculationRequestSerializer(serializers.Serializer):
    """Serializer for shipping calculation requests"""
    destination_address_id = serializers.IntegerField(
        required=False,
        help_text="ID of the destination address from customer's saved addresses"
    )
    destination_address = serializers.DictField(
        required=False,
        help_text="Manual destination address if not using saved address"
    )

    def validate(self, data):
        """Ensure either destination_address_id or destination_address is provided"""
        if not data.get('destination_address_id') and not data.get('destination_address'):
            raise serializers.ValidationError(
                "Either destination_address_id or destination_address must be provided"
            )
        return data


class ShippingCalculationResponseSerializer(serializers.Serializer):
    """Serializer for shipping calculation responses"""
    success = serializers.BooleanField()
    message = serializers.CharField()
    packing_cost = serializers.DecimalField(max_digits=6, decimal_places=2)
    shipping_cost = serializers.DecimalField(max_digits=6, decimal_places=2)
    total_weight = serializers.DecimalField(max_digits=8, decimal_places=2)
    total_volume = serializers.DecimalField(max_digits=12, decimal_places=4)
    shipping_options = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        help_text="Available shipping options with different carriers/services"
    )
    packing_details = serializers.DictField(
        required=False,
        help_text="Detailed packing information"
    )
    calculation_time = serializers.FloatField(
        required=False,
        help_text="Time taken for calculation in seconds"
    )


class CartItemSelectionSerializer(serializers.Serializer):
    """Serializer for updating cart item selection state"""
    is_selected = serializers.BooleanField()

    def update(self, instance, validated_data):
        instance.update_selection(validated_data['is_selected'])
        return instance


class BulkCartItemSelectionSerializer(serializers.Serializer):
    """Serializer for bulk cart item selection operations"""
    item_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        help_text="List of cart item IDs to select/deselect"
    )
    select_all = serializers.BooleanField(
        default=False,
        help_text="Select all items in cart"
    )
    deselect_all = serializers.BooleanField(
        default=False,
        help_text="Deselect all items in cart"
    )

    def validate(self, data):
        if data.get('select_all') and data.get('deselect_all'):
            raise serializers.ValidationError("Cannot select and deselect all at the same time")

        if not data.get('select_all') and not data.get('deselect_all') and not data.get('item_ids'):
            raise serializers.ValidationError("Must provide either item_ids or select_all/deselect_all")

        return data


class SelectedCartSummarySerializer(serializers.Serializer):
    """Serializer for selected cart items summary"""
    selected_items_count = serializers.IntegerField()
    total_items_count = serializers.IntegerField()
    selected_total_price = serializers.DecimalField(max_digits=10, decimal_places=2)
    selected_total_weight = serializers.FloatField()
    all_items_selected = serializers.BooleanField()
    selected_item_ids = serializers.ListField(child=serializers.IntegerField())
