from django.db import models
from django.apps import apps


class PaymentOption(models.Model):
    name = models.CharField(max_length=100, db_index=True)
    slug = models.SlugField(max_length=100, unique=True, default='')
    #  if the dataset is large, indexing is_active could speed up the .filter(is_active=True) query.
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Payment Options"


# class BraintreeTransaction(models.Model):
#     order = models.ForeignKey(Order, on_delete=models.CASCADE)
#     transaction_id = models.CharField(max_length=100)
#     amount = models.DecimalField(max_digits=10, decimal_places=2)
#     status = models.CharField(max_length=20)
#     payment_method = models.CharField(max_length=20)  # 'paypal' or 'card'
#     created_at = models.DateTimeField(auto_now_add=True)
#
#     class Meta:
#         ordering = ['-created_at']


class PayPalOrder(models.Model):
    order = models.OneToOneField('order.Order', on_delete=models.CASCADE)
    paypal_order_id = models.CharField(max_length=100, unique=True)
    status = models.CharField(max_length=20, default='CREATED')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"PayPal Order {self.paypal_order_id} for Order {self.order.id}"

    class Meta:
        verbose_name_plural = "PayPal Orders"
