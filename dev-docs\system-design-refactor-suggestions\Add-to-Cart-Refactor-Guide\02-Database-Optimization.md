# Database Optimization Guide

## Overview

Database operations are the primary bottleneck in the cart update workflow, accounting for 60-70% of response time. This guide provides step-by-step optimization strategies to reduce database load from 8,330 queries/second to under 1,000 queries/second.

## Current Database Issues

### N+1 Query Problems

**Problem**: Cart serializers trigger multiple database queries for each cart item.

**Current Pattern:**
```python
# apps/cart/serializers.py - Current inefficient pattern
class CartSerializer(ModelSerializer):
    cart_items = CartItemSerializer(many=True, read_only=True)
    
    def to_representation(self, instance):
        # This triggers N+1 queries:
        # 1 query for cart
        # N queries for each cart item's product/variant
        # N queries for each item's discounts
        # N queries for each item's images
        return super().to_representation(instance)
```

**Impact**: 10+ queries per cart operation instead of 1-2 optimized queries.

### Missing Database Indexes

**Current Issues:**
- No indexes on frequently queried foreign keys
- Missing composite indexes for complex queries
- No partial indexes for filtered queries

**Performance Impact:**
- Full table scans on cart_items table
- Slow lookups on product_variant relationships
- Poor performance on date-based queries

## Step-by-Step Optimization Plan

### Phase 1: Query Optimization (Week 1)

#### Step 1: Fix N+1 Queries in Cart Operations

**1.1 Optimize Cart ViewSet**

Create optimized queryset with proper prefetching:

```python
# apps/cart/views.py - Optimized version
class CartViewSet(CreateModelMixin, RetrieveModelMixin, DestroyModelMixin, GenericViewSet):
    serializer_class = CartSerializer

    def get_queryset(self):
        return Cart.objects.select_related(
            'customer'
        ).prefetch_related(
            Prefetch(
                'cart_items',
                queryset=CartItem.objects.select_related(
                    'product',
                    'product_variant',
                    'product__brand',
                    'product__category',
                    'product__product_type'
                ).prefetch_related(
                    Prefetch(
                        'product_variant__price_label',
                        queryset=AttributeValue.objects.select_related('attribute')
                    ),
                    'product_variant__product_image',
                    Prefetch(
                        'product_variant__discounts',
                        queryset=Discount.objects.filter(
                            is_active=True,
                            start_date__lte=timezone.now(),
                            end_date__gte=timezone.now()
                        ),
                        to_attr='active_discounts'
                    )
                )
            )
        )
```

**1.2 Optimize Cart Weight Calculation**

Replace Python loops with database aggregation:

```python
# apps/cart/models.py - Optimized version
class Cart(models.Model):
    # Remove the current get_cart_weight method and replace with:
    
    def get_cart_weight(self):
        """Calculate cart weight using database aggregation"""
        result = self.cart_items.aggregate(
            total_weight=Sum(
                F('quantity') * F('product_variant__weight'),
                output_field=DecimalField(max_digits=10, decimal_places=2)
            )
        )
        return result['total_weight'] or Decimal('0.00')
    
    def get_cart_volume(self):
        """Calculate cart volume using database aggregation"""
        result = self.cart_items.aggregate(
            total_volume=Sum(
                F('quantity') * F('product_variant__length') * 
                F('product_variant__width') * F('product_variant__height'),
                output_field=DecimalField(max_digits=12, decimal_places=4)
            )
        )
        return result['total_volume'] or Decimal('0.0000')
```

**1.3 Optimize CartItem Serializer**

Use prefetched data instead of triggering new queries:

```python
# apps/cart/serializers.py - Optimized version
class CartItemSerializer(ModelSerializer):
    total_price = serializers.SerializerMethodField()
    product_title = serializers.CharField(source='product.title', read_only=True)
    variant_sku = serializers.CharField(source='product_variant.sku', read_only=True)
    discounted_price = serializers.SerializerMethodField()

    def get_total_price(self, obj):
        """Use prefetched active_discounts to avoid additional queries"""
        active_discounts = getattr(obj.product_variant, 'active_discounts', [])
        if active_discounts:
            price = active_discounts[0].apply_discount(obj.product_variant.price)
        else:
            price = obj.product_variant.price
        return Decimal(str(price)) * obj.quantity

    def get_discounted_price(self, obj):
        """Get discounted price using prefetched data"""
        active_discounts = getattr(obj.product_variant, 'active_discounts', [])
        if active_discounts:
            return active_discounts[0].apply_discount(obj.product_variant.price)
        return obj.product_variant.price

    class Meta:
        model = CartItem
        fields = ['id', 'product_title', 'variant_sku', 'quantity', 
                 'total_price', 'discounted_price', 'created_at']
```

#### Step 2: Add Strategic Database Indexes

**2.1 Create Migration for Cart-Related Indexes**

```python
# Create migration: python manage.py makemigrations --empty cart
# apps/cart/migrations/XXXX_add_performance_indexes.py

from django.db import migrations

class Migration(migrations.Migration):
    dependencies = [
        ('cart', '0002_initial'),
    ]

    operations = [
        # Cart table indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS cart_customer_created_idx "
            "ON cart_cart (customer_id, created_at DESC);"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS cart_last_shipping_calc_idx "
            "ON cart_cart (last_shipping_calculation) WHERE last_shipping_calculation IS NOT NULL;"
        ),
        
        # CartItem table indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS cartitem_cart_product_variant_idx "
            "ON cart_cartitem (cart_id, product_id, product_variant_id);"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS cartitem_updated_at_idx "
            "ON cart_cartitem (updated_at DESC);"
        ),
        
        # Product and ProductVariant indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS product_active_title_idx "
            "ON products_product (is_active, title) WHERE is_active = true;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS productvariant_active_sku_idx "
            "ON products_productvariant (is_active, sku) WHERE is_active = true;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS productvariant_stock_idx "
            "ON products_productvariant (stock_qty) WHERE stock_qty > 0;"
        ),
    ]
```

**2.2 Add Shipping-Related Indexes**

```python
# apps/shipping/migrations/XXXX_add_shipping_indexes.py

from django.db import migrations

class Migration(migrations.Migration):
    dependencies = [
        ('shipping', '0001_initial'),
    ]

    operations = [
        # Box table indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS box_active_volume_cost_idx "
            "ON shipping_box (is_active, volume, cost) WHERE is_active = true;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS box_dimensions_idx "
            "ON shipping_box (internal_length, internal_width, internal_height) "
            "WHERE is_active = true;"
        ),
        
        # PackingRule table indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS packingrule_active_priority_idx "
            "ON shipping_packingrule (is_active, priority DESC) WHERE is_active = true;"
        ),
        
        # Carrier table indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS carrier_active_code_idx "
            "ON shipping_carrier (is_active, code) WHERE is_active = true;"
        ),
    ]
```

#### Step 3: Implement Database Connection Pooling

**3.1 Install and Configure pgbouncer**

```bash
# Install pgbouncer
sudo apt-get install pgbouncer

# Configure pgbouncer
sudo nano /etc/pgbouncer/pgbouncer.ini
```

**3.2 pgbouncer Configuration**

```ini
# /etc/pgbouncer/pgbouncer.ini
[databases]
picky_store = host=localhost port=5432 dbname=picky_store

[pgbouncer]
listen_port = 6432
listen_addr = 127.0.0.1
auth_type = md5
auth_file = /etc/pgbouncer/userlist.txt
logfile = /var/log/pgbouncer/pgbouncer.log
pidfile = /var/run/pgbouncer/pgbouncer.pid
admin_users = postgres
stats_users = stats, postgres

# Connection pooling settings
pool_mode = transaction
server_reset_query = DISCARD ALL
max_client_conn = 1000
default_pool_size = 100
min_pool_size = 10
reserve_pool_size = 20
reserve_pool_timeout = 5
max_db_connections = 200
max_user_connections = 100

# Performance tuning
server_round_robin = 1
ignore_startup_parameters = extra_float_digits
server_check_delay = 30
server_check_query = select 1
server_lifetime = 3600
server_idle_timeout = 600
```

**3.3 Update Django Database Configuration**

```python
# pc_hardware/settings/common.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'picky_store',
        'USER': 'your_user',
        'PASSWORD': 'your_password',
        'HOST': '127.0.0.1',
        'PORT': '6432',  # pgbouncer port instead of 5432
        'OPTIONS': {
            'MAX_CONNS': 100,
            'OPTIONS': {
                'MAX_CONNS': 100,
            }
        },
        'CONN_MAX_AGE': 600,  # Connection reuse
    }
}

# Add read replica for read-heavy operations
DATABASES['read_replica'] = {
    'ENGINE': 'django.db.backends.postgresql',
    'NAME': 'picky_store',
    'USER': 'readonly_user',
    'PASSWORD': 'readonly_password',
    'HOST': 'read-replica-host',
    'PORT': '6432',
    'OPTIONS': {
        'MAX_CONNS': 50,
    },
}

# Database routing for read operations
DATABASE_ROUTERS = ['apps.core.routers.DatabaseRouter']
```

### Phase 2: Advanced Optimizations (Week 2-3)

#### Step 4: Implement Database Aggregations

**4.1 Create Materialized Views for Heavy Queries**

```sql
-- Create materialized view for cart statistics
CREATE MATERIALIZED VIEW cart_stats AS
SELECT 
    c.id as cart_id,
    c.customer_id,
    COUNT(ci.id) as item_count,
    SUM(ci.quantity) as total_quantity,
    SUM(ci.quantity * pv.weight) as total_weight,
    SUM(ci.quantity * pv.length * pv.width * pv.height) as total_volume,
    MAX(ci.updated_at) as last_item_update
FROM cart_cart c
LEFT JOIN cart_cartitem ci ON c.id = ci.cart_id
LEFT JOIN products_productvariant pv ON ci.product_variant_id = pv.id
WHERE ci.id IS NOT NULL
GROUP BY c.id, c.customer_id;

-- Create index on materialized view
CREATE UNIQUE INDEX cart_stats_cart_id_idx ON cart_stats (cart_id);
CREATE INDEX cart_stats_customer_id_idx ON cart_stats (customer_id);

-- Refresh function
CREATE OR REPLACE FUNCTION refresh_cart_stats()
RETURNS TRIGGER AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY cart_stats;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger to refresh on cart changes
CREATE TRIGGER refresh_cart_stats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON cart_cartitem
    FOR EACH STATEMENT
    EXECUTE FUNCTION refresh_cart_stats();
```

**4.2 Use Materialized View in Django**

```python
# apps/cart/models.py
class CartStats(models.Model):
    cart_id = models.UUIDField(primary_key=True)
    customer_id = models.IntegerField(null=True)
    item_count = models.IntegerField(default=0)
    total_quantity = models.IntegerField(default=0)
    total_weight = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_volume = models.DecimalField(max_digits=12, decimal_places=4, default=0)
    last_item_update = models.DateTimeField(null=True)

    class Meta:
        managed = False  # Don't let Django manage this table
        db_table = 'cart_stats'

# Use in Cart model
class Cart(models.Model):
    # ... existing fields ...
    
    @property
    def stats(self):
        """Get cart statistics from materialized view"""
        try:
            return CartStats.objects.get(cart_id=self.id)
        except CartStats.DoesNotExist:
            return None
    
    def get_cart_weight_fast(self):
        """Fast cart weight using materialized view"""
        stats = self.stats
        return stats.total_weight if stats else Decimal('0.00')
```

#### Step 5: Optimize Shipping Calculations

**5.1 Create Shipping Cache Table**

```python
# apps/shipping/models/cache.py
class ShippingCalculationCache(models.Model):
    """Cache for shipping calculations to avoid recalculation"""
    
    cache_key = models.CharField(max_length=64, unique=True, db_index=True)
    cart_signature = models.CharField(max_length=255, db_index=True)
    destination_hash = models.CharField(max_length=64, db_index=True)
    
    # Cached results
    packing_cost = models.DecimalField(max_digits=6, decimal_places=2)
    shipping_cost = models.DecimalField(max_digits=6, decimal_places=2)
    total_weight = models.DecimalField(max_digits=8, decimal_places=2)
    total_volume = models.DecimalField(max_digits=12, decimal_places=4)
    packing_details = models.JSONField(default=dict)
    
    # Cache metadata
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(db_index=True)
    hit_count = models.PositiveIntegerField(default=0)
    
    class Meta:
        indexes = [
            models.Index(fields=['cache_key']),
            models.Index(fields=['expires_at']),
            models.Index(fields=['cart_signature', 'destination_hash']),
        ]
```

**5.2 Implement Cache-Aware Shipping Service**

```python
# apps/shipping/services/cached_shipping.py
import hashlib
from django.utils import timezone
from datetime import timedelta

class CachedShippingService:
    def __init__(self):
        self.cache_ttl = timedelta(hours=1)
    
    def generate_cache_key(self, cart_items, destination):
        """Generate deterministic cache key"""
        # Create signature from cart items
        items_data = []
        for item in cart_items:
            items_data.append({
                'sku': item.product_variant.sku,
                'quantity': item.quantity,
                'weight': float(item.product_variant.weight),
                'dimensions': [
                    float(item.product_variant.length),
                    float(item.product_variant.width),
                    float(item.product_variant.height)
                ]
            })
        
        # Sort for consistency
        items_data.sort(key=lambda x: x['sku'])
        
        # Create hash
        cart_signature = hashlib.md5(
            str(items_data).encode('utf-8')
        ).hexdigest()
        
        destination_hash = hashlib.md5(
            f"{destination.country}_{destination.postal_code}".encode('utf-8')
        ).hexdigest()
        
        cache_key = hashlib.md5(
            f"{cart_signature}_{destination_hash}".encode('utf-8')
        ).hexdigest()
        
        return cache_key, cart_signature, destination_hash
    
    def get_cached_calculation(self, cart_items, destination):
        """Get cached shipping calculation if available"""
        cache_key, cart_signature, destination_hash = self.generate_cache_key(
            cart_items, destination
        )
        
        try:
            cached = ShippingCalculationCache.objects.get(
                cache_key=cache_key,
                expires_at__gt=timezone.now()
            )
            
            # Update hit count
            cached.hit_count += 1
            cached.save(update_fields=['hit_count'])
            
            return {
                'success': True,
                'cached': True,
                'packing_cost': cached.packing_cost,
                'shipping_cost': cached.shipping_cost,
                'total_weight': cached.total_weight,
                'total_volume': cached.total_volume,
                'packing_details': cached.packing_details,
            }
            
        except ShippingCalculationCache.DoesNotExist:
            return None
    
    def cache_calculation(self, cart_items, destination, result):
        """Cache shipping calculation result"""
        cache_key, cart_signature, destination_hash = self.generate_cache_key(
            cart_items, destination
        )
        
        expires_at = timezone.now() + self.cache_ttl
        
        ShippingCalculationCache.objects.update_or_create(
            cache_key=cache_key,
            defaults={
                'cart_signature': cart_signature,
                'destination_hash': destination_hash,
                'packing_cost': result['packing_cost'],
                'shipping_cost': result['shipping_cost'],
                'total_weight': result['total_weight'],
                'total_volume': result['total_volume'],
                'packing_details': result['packing_details'],
                'expires_at': expires_at,
                'hit_count': 0,
            }
        )
```

## Performance Testing

### Before Optimization Benchmark

```bash
# Run load test before optimization
python manage.py test_cart_performance --users=50 --duration=60
```

### After Optimization Benchmark

```bash
# Run load test after optimization
python manage.py test_cart_performance --users=200 --duration=60
```

### Expected Improvements

| Metric | Before | After | Improvement |
|--------|--------|-------|-------------|
| Response Time (avg) | 1,200ms | 120ms | 10x faster |
| Database Queries | 10-12 per request | 1-2 per request | 5-10x reduction |
| Throughput | 41 req/sec | 400+ req/sec | 10x increase |
| CPU Usage | 90% | 30% | 3x reduction |
| Memory Usage | High | Moderate | 2x reduction |

## Monitoring and Maintenance

### Database Performance Monitoring

```python
# apps/core/management/commands/monitor_db_performance.py
from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    def handle(self, *args, **options):
        with connection.cursor() as cursor:
            # Monitor slow queries
            cursor.execute("""
                SELECT query, mean_time, calls, total_time
                FROM pg_stat_statements
                WHERE mean_time > 100
                ORDER BY mean_time DESC
                LIMIT 10;
            """)
            
            # Monitor index usage
            cursor.execute("""
                SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read
                FROM pg_stat_user_indexes
                WHERE idx_scan < 100
                ORDER BY idx_scan;
            """)
```

### Cache Hit Rate Monitoring

```python
# Monitor cache effectiveness
def monitor_shipping_cache():
    total_requests = ShippingCalculationCache.objects.count()
    cache_hits = ShippingCalculationCache.objects.filter(hit_count__gt=0).count()
    hit_rate = (cache_hits / total_requests) * 100 if total_requests > 0 else 0
    
    print(f"Shipping Cache Hit Rate: {hit_rate:.2f}%")
    return hit_rate
```

## Next Steps

1. **Implement Phase 1 optimizations** (Week 1)
2. **Monitor performance improvements** using provided scripts
3. **Proceed to Phase 2** if targets are not met
4. **Continue to Caching Strategy** (03-Caching-Strategy.md) for additional performance gains
