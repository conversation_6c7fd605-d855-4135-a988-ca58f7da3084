from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import BoxViewSet, CarrierViewSet, PackingRuleViewSet, ShippingCalculationViewSet

# Create router and register viewsets
router = DefaultRouter()
router.register(r'boxes', BoxViewSet)
router.register(r'carriers', CarrierViewSet)
router.register(r'rules', PackingRuleViewSet)
router.register(r'calculate', ShippingCalculationViewSet, basename='shipping-calculation')

app_name = 'shipping'

urlpatterns = [
    path('api/', include(router.urls)),
]
