from django.db import models
from django.db.models import Count, Sum, Avg
from django.utils import timezone
from datetime import timedelta
from apps.customers.models import Customer, Address


class CustomerProxy(Customer):
    """
    Proxy model for staff-specific customer operations
    Provides additional methods and behavior for staff customer management
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Customer"
        verbose_name_plural = "Staff Customers"
        permissions = [
            ("view_all_customers", "Can view all customers"),
            ("manage_customer_data", "Can manage customer data"),
            ("customer_analytics", "Can view customer analytics"),
            ("bulk_customer_operations", "Can perform bulk customer operations"),
            ("customer_support_access", "Can access customer support features"),
        ]

    def get_staff_summary(self):
        """Get summary information for staff dashboard"""
        return {
            'id': self.id,
            'full_name': f"{self.first_name} {self.last_name}".strip(),
            'email': self.user.email if self.user else None,
            'phone_number': str(self.user.phone_number) if self.user and self.user.phone_number else None,
            'birth_date': self.birth_date,
            'is_active': self.user.is_active if self.user else False,
            'date_joined': self.user.date_joined if self.user else None,
            'last_login': self.user.last_login if self.user else None,
            'orders_count': self.get_orders_count(),
            'total_spent': self.get_total_spent(),
            'addresses_count': self.address.count(),
            'wishlist_count': self.get_wishlist_count(),
            'customer_segment': self.get_customer_segment(),
        }

    def get_orders_count(self):
        """Get total number of orders for this customer"""
        return getattr(self, '_orders_count', self.order_set.count())

    def get_total_spent(self):
        """Get total amount spent by this customer"""
        from apps.order.models import Order
        total = Order.objects.filter(
            customer=self,
            payment_status='Paid'
        ).aggregate(total=Sum('total'))['total']
        return total or 0

    def get_wishlist_count(self):
        """Get number of items in customer's wishlist"""
        return getattr(self, '_wishlist_count', self.wishlists.count())

    def get_customer_segment(self):
        """Determine customer segment based on spending and activity"""
        total_spent = self.get_total_spent()
        orders_count = self.get_orders_count()
        
        if total_spent >= 1000 and orders_count >= 5:
            return 'VIP'
        elif total_spent >= 500 and orders_count >= 3:
            return 'LOYAL'
        elif orders_count >= 2:
            return 'REPEAT'
        elif orders_count == 1:
            return 'NEW'
        else:
            return 'PROSPECT'

    def get_recent_activity(self, days=30):
        """Get recent customer activity"""
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # Recent orders
        recent_orders = self.order_set.filter(
            placed_at__gte=cutoff_date
        ).order_by('-placed_at')[:5]
        
        # Recent wishlist additions
        recent_wishlist = self.wishlists.filter(
            added_at__gte=cutoff_date
        ).order_by('-added_at')[:5]
        
        return {
            'recent_orders': recent_orders,
            'recent_wishlist_items': recent_wishlist,
            'last_login': self.user.last_login if self.user else None,
        }

    def get_support_history(self):
        """Get customer support interaction history"""
        # This would integrate with a support ticket system
        # For now, return placeholder structure
        return {
            'total_tickets': 0,
            'open_tickets': 0,
            'satisfaction_rating': None,
            'last_contact': None,
        }


class AddressProxy(Address):
    """
    Proxy model for staff-specific address operations
    """

    class Meta:
        proxy = True
        verbose_name = "Staff Address"
        verbose_name_plural = "Staff Addresses"
        permissions = [
            ("view_all_addresses", "Can view all customer addresses"),
            ("manage_customer_addresses", "Can manage customer addresses"),
        ]

    def get_staff_summary(self):
        """Get summary information for staff dashboard"""
        return {
            'id': self.id,
            'customer_id': self.customer.id,
            'customer_name': f"{self.customer.first_name} {self.customer.last_name}".strip(),
            'full_name': self.full_name,
            'full_address': self.get_full_address(),
            'city': self.city_or_village,
            'postal_code': self.postal_code,
            'is_primary': self.is_primary_address(),
            'orders_count': self.get_orders_count(),
        }

    def get_full_address(self):
        """Get formatted full address"""
        parts = [
            self.street_name,
            self.address_line_1,
            self.address_line_2,
            self.city_or_village,
            self.postal_code
        ]
        return ', '.join(filter(None, parts))

    def is_primary_address(self):
        """Check if this is the customer's primary/most used address"""
        # Simple heuristic: most recently used address for orders
        from apps.order.models import Order
        recent_orders = Order.objects.filter(
            customer=self.customer,
            selected_address=self
        ).count()
        
        # Compare with other addresses
        other_addresses_usage = Order.objects.filter(
            customer=self.customer
        ).exclude(selected_address=self).values('selected_address').annotate(
            count=Count('id')
        ).order_by('-count').first()
        
        if not other_addresses_usage:
            return True
            
        return recent_orders >= other_addresses_usage['count']

    def get_orders_count(self):
        """Get number of orders delivered to this address"""
        from apps.order.models import Order
        return Order.objects.filter(
            customer=self.customer,
            selected_address=self
        ).count()
