# Google Authentication Workflow in the Django App

This document explains the step-by-step process of how Google OAuth2 authentication is integrated into this Django application. It includes how Google login works, how tokens are handled, and the role of access and refresh tokens for user sessions.

## Overview

The app allows users to authenticate using their Google accounts via OAuth2. Once authenticated, the app creates local **JWT (JSON Web Token)** access and refresh tokens, which are used to manage user sessions and access the app’s resources.

The workflow uses the `social-auth-app-django` library to handle Google OAuth2 and the Django Rest Framework (DRF) for API interactions.

Google Authentication Flow

### Step 1: User Initiates Google Login

1. The frontend prompts the user to log in via Google.
2. Upon clicking the "Sign in with Google" button, the frontend redirects the user to the Google OAuth2 authorization page.
3. After the user authenticates with Google, Google returns an**access token** to the frontend.

### Step 2: Exchange Google Access Token for Local JWT Tokens

Once the frontend has received the Google access token, it makes a **POST request** to the backend with the Google access token as follows:

```http
// POST /auth/google/

{
    "access_token": "google_access_token"
}
```

### Step 3: Backend Processes Google Access Token

In the backend, the following steps occur:

1. **Validate Google Access Token** : The backend receives the Google access token and uses the`social-auth-app-django` to verify and authenticate the user with Google.
   * If valid, it either registers a new user or retrieves the existing user from the local database.
2. **Generate Local JWT Tokens** : After Google authentication is successful, the backend generates local**JWT access and refresh tokens** for the user using Django’s`RefreshToken` mechanism.

### Step 4: Set JWT Tokens in Cookies

The backend sets the JWT tokens (both **access** and **refresh** tokens) in **HTTP-only, secure cookies** for session management. This is done to ensure that the tokens are not exposed to JavaScript (protecting against XSS attacks) and are only sent over secure HTTPS connections.

* **Access Token** : A short-lived token (valid for about 1 hour) used for authenticating API requests.
* **Refresh Token** : A long-lived token used to refresh the access token without requiring the user to log in again.

Here’s the code for setting the cookies in the `GoogleLoginView`:

```python
# Set JWT access and refresh tokens in HTTP-only cookies
response.set_cookie(
    'access',
    value=access_token,
    httponly=True,  # Not accessible via JavaScript
    secure=True,    # Only sent over HTTPS
    samesite='Lax', # Prevent CSRF attacks
    max_age=3600,   # 1-hour expiration for access token
)

response.set_cookie(
    'refresh',
    value=str(refresh_token),
    httponly=True,  # Not accessible via JavaScript
    secure=True,    # Only sent over HTTPS
    samesite='Lax', # Prevent CSRF attacks
    max_age=14 * 24 * 3600,  # Longer expiration for refresh token (e.g., 2 weeks)
)
```

### Step 5: Accessing Protected Resources

Once the user is authenticated and has JWT tokens stored in cookies, the frontend can make authenticated API requests by sending the **access token** stored in the cookie along with each request.

The backend verifies the access token to ensure the user is authorized.

### Step 6: Refreshing the Access Token

When the **access token** expires (after about 1 hour), the frontend automatically sends a **POST request** to refresh the access token using the **refresh token** stored in the cookie:

```http
POST /auth/refresh/
```

If the refresh token is valid, the backend issues a new access token and sets it in the HTTP-only cookie, ensuring the user doesn’t need to log in again.

## Code Structure

### Google Login View

Here’s the code for the Google login flow in the backend:

```python
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from social_django.utils import load_backend, load_strategy
from rest_framework_simplejwt.tokens import RefreshToken

class GoogleLoginView(APIView):
    """
    API view to handle Google OAuth2 login.
    This endpoint expects a Google access token and responds with JWT tokens (access and refresh).
    """

    def post(self, request, *args, **kwargs):
        # Get the Google access token from the request body
        google_token = request.data.get('access_token')

        # If no access token is provided, return a 400 Bad Request response
        if not google_token:
            return Response({'error': 'Google access token is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Initialize the social-auth strategy for the current request
            strategy = load_strategy(request)

            # Load the Google OAuth2 backend using the strategy
            # This backend helps verify and authenticate the user based on the Google token
            backend = load_backend(strategy=strategy, name='google-oauth2', redirect_uri=None)

            # Authenticate the user with the provided Google token
            # If successful, it returns the user instance (either existing or new)
            user = backend.do_auth(google_token)
        except Exception as e:
            # If there's an error during the authentication process, return a 400 response with the error message
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

        # If the user exists and is active in the system, proceed to create JWT tokens
        if user and user.is_active:
            # Generate JWT tokens (both access and refresh) for the authenticated user
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)

            # Prepare the response with both tokens to be sent back to the client
            response = Response({
                'refresh_token': str(refresh),  # The refresh token
                'access_token': access_token,   # The access token
            }, status=status.HTTP_200_OK)

            # Set the access token in a secure, HTTP-only cookie
            # - httponly: Prevents JavaScript access (helps protect against XSS attacks)
            # - secure: Ensures the cookie is only sent over HTTPS (enable in production)
            # - samesite: Limits the cookie to same-site requests to mitigate CSRF attacks
            # - max_age: Cookie expires after 1 hour (same as the access token's expiration)
            response.set_cookie(
                key='access',
                value=access_token,
                httponly=True,
                secure=True,
                samesite='Lax',  # Default value, adjusts cross-site request protection
                max_age=3600,     # 1 hour expiration for the access token
            )

            # Set the refresh token in another secure, HTTP-only cookie
            # - This cookie has a longer expiration (e.g., 14 days), so the user can refresh the access token without re-logging
            response.set_cookie(
                key='refresh',
                value=str(refresh),
                httponly=True,
                secure=True,
                samesite='Lax',
                max_age=14 * 24 * 3600,  # 14 days expiration for refresh token
            )

            # Return the response to the client with both JWT tokens set in cookies and body
            return response

        # If the user is not authenticated or active, return an Unauthorized response
        return Response({'error': 'Authentication failed'}, status=status.HTTP_401_UNAUTHORIZED)
```

### Breakdown of Key Steps:

1. **Getting the Google Token** : The view first extracts the Google access token sent by the frontend.
2. **OAuth2 Verification** : The Google token is validated by loading the`google-oauth2` backend using`social-auth-app-django`. If valid, a local user (existing or new) is authenticated.
3. **JWT Token Generation** : After successful authentication, both access and refresh JWT tokens are created using the`RefreshToken` class.
4. **Setting Tokens in Cookies** :
   * The**access token** is stored in an HTTP-only cookie with a 1-hour expiration.
   * The**refresh token** is stored in another HTTP-only cookie with a longer expiration (e.g., 14 days) to allow the access token to be refreshed without re-logging.
5. **Response to Client** : The tokens are also returned in the response body (in addition to being set as cookies), allowing flexibility in token management.
6. **Error Handling** : If any part of the authentication process fails (invalid token, exception), appropriate error responses (400/401) are returned.

## Key Points for New Developers

1. **Local JWT Token Management** : We use**local JWT tokens** (both access and refresh tokens) to manage user sessions, even if the user logs in through Google OAuth2.
2. **Cookie-based Token Storage** : Both access and refresh tokens are stored in**secure, HTTP-only cookies** to ensure they are protected from client-side JavaScript.
3. **Google Access Token** : The Google access token is used**only once** during login to authenticate the user. After that, we use our own JWT tokens for session management.
4. **Token Refresh Flow** : When the local JWT access token expires, the refresh token is used to obtain a new access token without requiring the user to log in again.
5. **Security** : Ensure that tokens are stored securely using the`httponly`,`secure`, and`samesite` cookie options to prevent CSRF and XSS attacks.
