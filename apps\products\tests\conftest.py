from rest_framework.test import APIClient
import pytest
from django.contrib.auth import get_user_model
from model_bakery import baker
from apps.products.models import (
    Category, Product, ProductVariant, ProductType, Brand,
    Attribute, AttributeValue, ProductImage, Review
)
from apps.customers.models import Customer

User = get_user_model()


@pytest.fixture
def api_client():
    """Create API client for testing"""
    return APIClient()


@pytest.fixture
def admin_user():
    """Create admin user"""
    return baker.make(User, is_staff=True, is_superuser=True)


@pytest.fixture
def regular_user():
    """Create regular user"""
    return baker.make(User, is_staff=False)


@pytest.fixture
def customer():
    """Create customer"""
    return baker.make(Customer)


@pytest.fixture
def authenticated_admin_client(api_client, admin_user):
    """Create authenticated admin API client"""
    api_client.force_authenticate(user=admin_user)
    return api_client


@pytest.fixture
def authenticated_user_client(api_client, regular_user):
    """Create authenticated regular user API client"""
    api_client.force_authenticate(user=regular_user)
    return api_client


@pytest.fixture
def category():
    """Create a test category"""
    return baker.make(Category, title="Test Category", slug="test-category")


@pytest.fixture
def category_with_parent():
    """Create a test category with parent"""
    parent = baker.make(Category, title="Parent Category", slug="parent")
    return baker.make(Category, title="Child Category", parent=parent)


@pytest.fixture
def brand():
    """Create a test brand"""
    return baker.make(Brand, title="Test Brand", slug="test-brand")


@pytest.fixture
def product_type():
    """Create a test product type"""
    return baker.make(ProductType, title="Test Product Type")


@pytest.fixture
def attribute():
    """Create a test attribute"""
    return baker.make(Attribute, title="Color")


@pytest.fixture
def attribute_value(attribute):
    """Create a test attribute value"""
    return baker.make(AttributeValue, attribute=attribute, attribute_value="Red")


@pytest.fixture
def product(category, brand, product_type):
    """Create a test product with all required relationships"""
    return baker.make(
        Product,
        title="Test Product",
        slug="test-product",
        category=category,
        brand=brand,
        product_type=product_type
    )


@pytest.fixture
def product_variant(product):
    """Create a test product variant"""
    return baker.make(
        ProductVariant,
        product=product,
        price=99.99,
        sku="TEST-SKU",
        stock_qty=10,
        weight=100.00,
        is_active=True
    )


@pytest.fixture
def product_image(product_variant):
    """Create a test product image"""
    return baker.make(
        ProductImage,
        product_variant=product_variant,
        alternative_text="Test image",
        image="test.jpg"
    )


@pytest.fixture
def review(product, customer):
    """Create a test review"""
    return baker.make(
        Review,
        product=product,
        customer=customer,
        title="Great product",
        description="Really love this product!",
        rating=5
    )


@pytest.fixture
def complete_product_setup(category, brand, product_type, customer):
    """Create a complete product setup with all related objects"""
    product = baker.make(
        Product,
        title="Complete Product",
        slug="complete-product",
        category=category,
        brand=brand,
        product_type=product_type
    )

    variant = baker.make(
        ProductVariant,
        product=product,
        price=199.99,
        sku="COMPLETE-SKU",
        stock_qty=5,
        weight=200.00,
        is_active=True
    )

    image = baker.make(
        ProductImage,
        product_variant=variant,
        alternative_text="Complete product image",
        image="complete.jpg"
    )

    review = baker.make(
        Review,
        product=product,
        customer=customer,
        title="Complete review",
        description="This is a complete product setup",
        rating=4
    )

    return {
        'product': product,
        'variant': variant,
        'image': image,
        'review': review
    }


# Legacy fixture for backward compatibility
@pytest.fixture
def authenticate(api_client):
    def do_authenticate(user=None):
        if user:
            api_client.force_authenticate(user=user)
        return api_client

    return do_authenticate
