---
type: "agent_requested"
description: "Rules for Django best practices"
---
<Models>
- Use UUIDField for primary keys (if applicable).
- Always define __str__() method.

<URLs / Routing>

- Use DRF routers with named URL patterns.
- Use namespacing for modularity.
- Avoid manually defining routes unless necessary.

<Permissions & Auth>

- Use DRF permission classes (IsAuthenticated, IsAdminUser, or custom).

<Performance>
- Use select_related, prefetch_related to reduce queries.
- Add a comment about the model field if indexing can boost performance.
- Use pagination for all list endpoints.

<Testing>
- Use pytest or pytest-django for Django TestCase.
- Maintain ≥90% coverage on all new/modified code.
- Cover permissions, edge cases, and logic.
- Do not test Django/DRF internals.
