# Selective Checkout System Overview

This document provides a high-level overview of how the selective checkout system works, designed for new developers to understand the complete flow from frontend to backend.

## System Architecture

```mermaid
graph TB
    A[User Interface] --> B[Cart Store]
    B --> C[Selection Hooks]
    C --> D[Backend APIs]
    D --> E[Database]

    F[Cart Items] --> G[Selection State]
    G --> H[Price Calculation]
    H --> I[Shipping Calculation]
    I --> J[Order Creation]

    K[Frontend Components] --> L[PriceSummary]
    K --> M[CartItemsList]
    M --> N[Selection Checkboxes]
```

## Core Concepts

### 1. Selection State Management

- **Frontend**: Zustand store manages which items are selected
- **Backend**: Database tracks `is_selected` (boolean). The timestamp field `selected_at` is not present in the current implementation.
- **Synchronization**: Frontend syncs with backend on cart load

### 2. Dual Pricing System

- **All Items**: Traditional cart totals (total_price, shipping_cost, etc.)
- **Selected Items**: New totals for selected items only (selected_total_price, selected_shipping_cost, etc.)

### 3. Smart Cart Persistence

- **Traditional**: Cart deleted after order creation
- **New**: Only selected items removed, unselected items remain

## Step-by-Step Flow

### Phase 1: Cart Building

1. **User adds items to cart**

   - Items added with `is_selected = true` by default
   - Frontend store updated with selection state
   - Backend saves item with selection fields

2. **User views cart**
   - Frontend loads cart data from backend
   - Selection state synchronized between frontend/backend
   - Both all-items and selected-items totals calculated

### Phase 2: Item Selection

1. **User toggles item selection**

   ```typescript
   // Frontend: User clicks checkbox
   toggleItemSelection(itemId) →
   // Hook: API call to backend
   PATCH /cart/{cart_id}/items/{item_id}/select/ →
   // Backend: Update database
   CartItem.update(is_selected) →
   // Frontend: Update store and UI
   ```

2. **Bulk operations**

   ```typescript
   // Select All
   bulkSelect({ select_all: true }) →
   POST /cart/{cart_id}/items/bulk-select/ →
   // Updates all items in single transaction
   ```

### Phase 3: Price Calculation

1. **Real-time totals**

   - Frontend calculates selected totals from cart data
   - PriceSummary component shows selected vs all items
   - Visual indicators for selection state

2. **Shipping calculation**

   ```typescript
   // User requests shipping calculation
   POST /cart/{cart_id}/calculate-shipping/ {
     selected_only: true
   } →
   // Backend: Calculate for selected items only
   OnDemandShippingService.calculate_shipping_for_cart(
     cart, selected_only=true
   ) →
   // Returns shipping cost for selected items
   ```

### Phase 4: Checkout Process

1. **Address selection**

   - User selects delivery address
   - Shipping calculated for selected items only

2. **Payment method selection**

   - User chooses payment method
   - Order preview shows selected items totals

3. **Order creation**

   ```typescript
   // Frontend: Submit order with selected items
   POST /orders/ {
     cart_id,
     selected_cart_item_ids: [1, 2, 3],
     selected_address,
     payment_method
   } →
   // Backend: Process selected items only
   CreateOrderSerializer.save() →
   // Creates order from selected items
   // Removes processed items from cart
   // Keeps unselected items in cart
   ```

## Data Flow Diagram

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant S as Store
    participant A as API
    participant D as Database

    U->>F: Toggle item selection
    F->>S: Update selection state
    F->>A: PATCH /items/{id}/select/
    A->>D: Update is_selected field
    D-->>A: Confirm update
    A-->>F: Return updated item
    F->>S: Sync selection state
    S-->>F: Trigger UI update
    F-->>U: Show updated selection

    U->>F: Request checkout
    F->>A: POST /calculate-shipping/
    A->>D: Get selected items
    A->>A: Calculate shipping for selected
    A-->>F: Return shipping costs
    F-->>U: Show checkout summary

    U->>F: Place order
    F->>A: POST /orders/ with selected_cart_item_ids
    A->>D: Create order from selected items
    A->>D: Remove processed items from cart
    A-->>F: Return order + cart info
    F-->>U: Show order confirmation
```

## Key Components

### Frontend Components

#### 1. CartStore (Zustand)

```typescript
interface CartStoreShape {
  selectedCartItems: Set<number> // Selected item IDs
  toggleItemSelection: (itemId: number) => void
  toggleSelectAll: (allItemIds: number[]) => void
  // ... other methods
}
```

#### 2. Selection Hooks

- `useCartItemSelection()`: Toggle individual items
- `useBulkCartSelection()`: Bulk select/deselect operations
- `useSyncCartSelection()`: Sync frontend with backend

#### 3. PriceSummary Component

- Shows selected items summary when selection is active
- Displays comparison between selected vs all items
- Handles both traditional and selective checkout modes

### Backend Components

#### 1. CartItem Model

```python
class CartItem(models.Model):
   # ... existing fields
   is_selected = models.BooleanField(default=True)
   # Note: selected_at timestamp is not stored in the current models; selection is persisted as a boolean only.
```

#### 2. Selection API Endpoints

- `PATCH /cart/{cart_id}/items/{item_id}/select/`: Toggle selection
- `POST /cart/{cart_id}/items/bulk-select/`: Bulk select
- `POST /cart/{cart_id}/items/bulk-deselect/`: Bulk deselect
- `GET /cart/{cart_id}/selected-summary/`: Get selection summary

#### 3. Enhanced Serializers

- `CartSerializer`: Includes selected totals
- `CartItemSelectionSerializer`: Handles selection operations
- `CreateOrderSerializer`: Processes selected items only

## Error Handling

### Frontend

- Optimistic updates with rollback on API failure
- Loading states during bulk operations
- Validation before checkout (must have selected items)

### Backend

- Transaction safety for bulk operations
- Validation of selected item IDs
- Graceful handling of empty selections

## Performance Considerations

### Database Optimization

- Indexes on `(cart, is_selected)` (the previous suggestion including `selected_at` is no longer applicable).
- Efficient queries for selected items only
- Bulk operations to minimize database calls

### Frontend Optimization

- Debounced selection updates
- Efficient Set operations for selection state
- Minimal re-renders with proper React optimization

## Testing Strategy

### Unit Tests

- Selection state management
- Price calculation logic
- Order creation with selected items

### Integration Tests

- Complete checkout flow with selection
- Cart persistence after partial checkout
- Shipping calculation for selected items

### E2E Tests

- User selection workflows
- Multi-item checkout scenarios
- Edge cases (empty selection, all selected, etc.)

## Migration Path

### For Existing Carts

1. Add selection fields with migration
2. Set `is_selected = true` for existing items
3. Gradual rollout with feature flags
4. Backward compatibility maintained

### For Existing Code

1. Enhanced components are backward compatible
2. New props are optional with sensible defaults
3. Existing checkout flow works unchanged
4. Progressive enhancement approach

## Troubleshooting

### Common Issues

1. **Selection state mismatch**: Check frontend-backend sync
2. **Empty selection at checkout**: Validate selection before order creation
3. **Performance issues**: Review database indexes and query optimization
4. **Cart persistence issues**: Verify cart cleanup logic

### Debug Tools

1. Browser dev tools for store state
2. Django admin for database inspection
3. API logs for request/response debugging
4. Postman for API testing

This system provides a robust foundation for selective checkout while maintaining backward compatibility and performance.
