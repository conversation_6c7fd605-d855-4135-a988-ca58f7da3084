from rest_framework import serializers
from django.db.models import Count, Sum, Avg
from django.utils import timezone
from datetime import timedelta
from .models import WishlistProxy
from apps.customers.serializers import SimpleCustomerSerializer
from apps.products.serializers import SimpleProductSerializer


class WishlistStaffSerializer(serializers.ModelSerializer):
    """Serializer for staff wishlist management"""
    customer = SimpleCustomerSerializer(read_only=True)
    product = SimpleProductSerializer(read_only=True)
    current_price = serializers.SerializerMethodField()
    days_in_wishlist = serializers.SerializerMethodField()
    conversion_potential = serializers.SerializerMethodField()
    marketing_insights = serializers.SerializerMethodField()
    
    class Meta:
        model = WishlistProxy
        fields = [
            'id', 'customer', 'product', 'added_at',
            'current_price', 'days_in_wishlist', 'conversion_potential',
            'marketing_insights'
        ]
    
    def get_current_price(self, obj):
        return obj.get_current_price()
    
    def get_days_in_wishlist(self, obj):
        return obj.get_days_in_wishlist()
    
    def get_conversion_potential(self, obj):
        return obj.get_conversion_potential()
    
    def get_marketing_insights(self, obj):
        return obj.get_marketing_insights()


class WishlistSummarySerializer(serializers.ModelSerializer):
    """Serializer for wishlist summary/list view"""
    customer_name = serializers.SerializerMethodField()
    customer_email = serializers.SerializerMethodField()
    product_title = serializers.SerializerMethodField()
    product_category = serializers.SerializerMethodField()
    current_price = serializers.SerializerMethodField()
    days_in_wishlist = serializers.SerializerMethodField()
    conversion_potential = serializers.SerializerMethodField()
    
    class Meta:
        model = WishlistProxy
        fields = [
            'id', 'customer_name', 'customer_email', 'product_title',
            'product_category', 'current_price', 'days_in_wishlist',
            'conversion_potential', 'added_at'
        ]
    
    def get_customer_name(self, obj):
        return f"{obj.customer.first_name} {obj.customer.last_name}".strip()
    
    def get_customer_email(self, obj):
        return obj.customer.user.email if obj.customer.user else None
    
    def get_product_title(self, obj):
        return obj.product.title
    
    def get_product_category(self, obj):
        return obj.product.category.title if obj.product.category else None
    
    def get_current_price(self, obj):
        return obj.get_current_price()
    
    def get_days_in_wishlist(self, obj):
        return obj.get_days_in_wishlist()
    
    def get_conversion_potential(self, obj):
        return obj.get_conversion_potential()


class WishlistAnalyticsSerializer(serializers.Serializer):
    """Serializer for wishlist analytics data"""
    total_wishlist_items = serializers.IntegerField()
    unique_customers = serializers.IntegerField()
    unique_products = serializers.IntegerField()
    average_items_per_customer = serializers.FloatField()
    conversion_rate = serializers.FloatField()
    abandonment_rate = serializers.FloatField()
    top_wishlist_products = serializers.ListField()
    wishlist_trends = serializers.DictField()
    customer_segments = serializers.DictField()
    category_distribution = serializers.DictField()
    price_range_distribution = serializers.DictField()


class CustomerBehaviorSerializer(serializers.Serializer):
    """Serializer for customer behavior analysis"""
    customer_wishlist_patterns = serializers.DictField()
    seasonal_trends = serializers.DictField()
    conversion_patterns = serializers.DictField()
    abandonment_patterns = serializers.DictField()
    cross_category_preferences = serializers.DictField()
    price_sensitivity_analysis = serializers.DictField()


class MarketingInsightsSerializer(serializers.Serializer):
    """Serializer for marketing insights"""
    conversion_opportunities = serializers.ListField()
    discount_recommendations = serializers.DictField()
    email_campaign_targets = serializers.DictField()
    product_promotion_suggestions = serializers.ListField()
    customer_retention_insights = serializers.DictField()
    revenue_potential = serializers.DictField()


class BulkWishlistOperationSerializer(serializers.Serializer):
    """Serializer for bulk wishlist operations"""
    wishlist_ids = serializers.ListField(
        child=serializers.IntegerField(),
        allow_empty=False
    )
    operation = serializers.ChoiceField(choices=[
        ('remove', 'Remove from Wishlist'),
        ('export', 'Export Wishlist Data'),
        ('marketing_campaign', 'Create Marketing Campaign'),
        ('price_alert', 'Set Price Alert'),
    ])
    reason = serializers.CharField(max_length=500, required=False)
    campaign_type = serializers.ChoiceField(
        choices=[
            ('discount', 'Discount Offer'),
            ('reminder', 'Gentle Reminder'),
            ('urgency', 'Urgency Campaign'),
            ('abandonment', 'Abandonment Recovery'),
        ],
        required=False
    )
    discount_percentage = serializers.IntegerField(min_value=5, max_value=50, required=False)


class ConversionOpportunitySerializer(serializers.Serializer):
    """Serializer for conversion opportunities"""
    wishlist_item_id = serializers.IntegerField()
    customer_email = serializers.CharField()
    product_title = serializers.CharField()
    conversion_potential = serializers.IntegerField()
    recommended_action = serializers.CharField()
    target_discount = serializers.IntegerField()
    urgency_level = serializers.CharField()
    days_in_wishlist = serializers.IntegerField()
    customer_segment = serializers.CharField()


class WishlistTrendSerializer(serializers.Serializer):
    """Serializer for wishlist trend data"""
    date = serializers.DateField()
    additions = serializers.IntegerField()
    removals = serializers.IntegerField()
    conversions = serializers.IntegerField()
    net_change = serializers.IntegerField()


class ProductWishlistStatsSerializer(serializers.Serializer):
    """Serializer for product wishlist statistics"""
    product_id = serializers.IntegerField()
    product_title = serializers.CharField()
    wishlist_count = serializers.IntegerField()
    conversion_rate = serializers.FloatField()
    average_days_to_conversion = serializers.FloatField()
    abandonment_rate = serializers.FloatField()
    revenue_potential = serializers.DecimalField(max_digits=10, decimal_places=2)


class CustomerWishlistBehaviorSerializer(serializers.Serializer):
    """Serializer for individual customer wishlist behavior"""
    customer_id = serializers.IntegerField()
    customer_email = serializers.CharField()
    total_wishlist_items = serializers.IntegerField()
    average_time_to_purchase = serializers.FloatField()
    conversion_rate = serializers.FloatField()
    favorite_categories = serializers.ListField()
    price_sensitivity = serializers.CharField()
    engagement_level = serializers.CharField()
