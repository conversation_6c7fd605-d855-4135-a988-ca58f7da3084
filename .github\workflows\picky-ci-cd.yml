#name: CI - Feature Branch Testing
#
#on: # Events that trigger the workflow
#  push:# Runs when changes are pushed to the main branch
#    # branches: [ main ]
#  pull_request:# Runs when a pull request targets the main branch
#    # branches: [ main ]
#
#jobs:
#  test:
#    runs-on: ubuntu-latest # Specifies the runner environment (latest Ubuntu)
#
#    services:
#      postgres: # Defines a PostgreSQL database service
#        image: postgres:16.6-alpine # Uses a lightweight Alpine-based PostgreSQL image
#        env: # Environment variables for database configuration
#          POSTGRES_DB: mydatabase
#          POSTGRES_USER: myuser
#          POSTGRES_PASSWORD: mypassword
#        ports:
#          - 5432:5432 # Maps the PostgreSQL port for communication
#        options:
#          >- # Health check to ensure the database is ready before tests run
#          --health-cmd="pg_isready -U myuser -d mydatabase"
#          --health-interval=10s
#          --health-timeout=5s
#          --health-retries=5
#
#    steps:
#      - name: Checkout repository # Fetches the repository code
#        uses: actions/checkout@v4
#
#      - name: Set up Python # Installs the specified Python version
#        uses: actions/setup-python@v4
#        with:
#          python-version: "3.13"
#
#      - name: Install dependencies # Installs required Python dependencies using Pipenv
#        run: |
#          python -m pip install --upgrade pip  # Upgrade pip first (optional but recommended)
#          pip install pipenv  # Ensure Pipenv is installed
#          pipenv install --dev  # Install dependencies, including development dependencies
#
#      - name: Run Migrations
#        env:
#          DATABASE_URL: postgresql://myuser:mypassword@localhost:5432/mydatabase
#        run: |
#          pipenv run python manage.py migrate
#
#      - name: Run Tests
#        #        env:
#        #          DATABASE_URL: postgresql://myuser:mypassword@localhost:5432/mydatabase
#        run: pipenv run pytest
