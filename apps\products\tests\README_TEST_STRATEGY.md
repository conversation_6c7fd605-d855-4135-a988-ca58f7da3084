# Products App Test Strategy

## Overview
This document outlines the comprehensive test strategy for the products app, covering all models, views, serializers, and business logic.

## Test Structure

### 1. Model Tests (`test_models.py`)
**Priority: HIGH**

#### Category Model Tests
- ✅ Slug auto-generation from title
- ✅ Slug concatenation with parent
- ✅ Unique together constraint (slug, parent)
- ✅ String representation with hierarchy
- ✅ MPTT tree structure validation

#### Product Model Tests
- ✅ Average rating calculation
- ✅ Rating update with multiple reviews
- ✅ Rating reset when no reviews
- ✅ Required field validation
- ✅ String representation

#### ProductVariant Model Tests
- ✅ Price minimum validation (≥ 1.00)
- ✅ Weight minimum validation (≥ 0.00)
- ✅ SKU uniqueness constraint
- ✅ Order field duplicate validation
- ✅ Condition choices validation
- ✅ Custom clean() method validation

#### ProductImage Model Tests
- ✅ Order field auto-assignment
- ✅ Order duplicate validation
- ✅ String representation

#### Review Model Tests
- ✅ Rating range validation (1-5)
- ✅ Automatic product rating update on save
- ✅ String representation

#### ProductVariantAttributeValue Model Tests
- ✅ Unique together constraint
- ✅ Duplicate attribute type prevention
- ✅ Complex validation scenarios

### 2. API Endpoint Tests (`test_api_endpoints.py`)
**Priority: HIGH**

#### CategoryViewSet Tests
- ✅ List categories (anonymous access)
- ✅ Retrieve category (anonymous access)
- ✅ Create category (admin only - 401 for anonymous)
- ✅ Create category with parent
- ✅ Update category (admin only)
- ✅ Delete category (admin only)
- ✅ Validation error handling
- ✅ Duplicate slug validation

#### ProductViewSet Tests
- ✅ List products with pagination
- ✅ Retrieve product by slug
- ✅ List products by category slug
- ✅ Category hierarchy inclusion
- ✅ Search filtering
- ✅ Price range filtering
- ✅ Brand filtering
- ✅ Condition filtering
- ✅ Sorting (title, price)
- ✅ 404 for non-existent products
- ✅ Method restrictions (GET only)

#### ProductFilterOptionsViewSet Tests
- ✅ Filter options without product type
- ✅ Filter options with product type
- ✅ Price range calculation
- ✅ Brand filtering by product type

#### ProductTypeAttributeValueViewSet Tests
- ✅ Get attribute values for product type
- ✅ Invalid product type handling
- ✅ Missing product type handling

#### ReviewViewSet Tests (Nested Routes)
- ✅ List product reviews
- ✅ Create product review
- ✅ Review updates product rating
- ✅ Retrieve specific review
- ✅ Rating validation
- ✅ Non-existent product handling

### 3. Serializer Tests (`test_serializers.py`)
**Priority: MEDIUM**

#### CategorySerializer Tests
- ✅ Serialization with all fields
- ✅ Valid data deserialization
- ✅ Invalid data handling
- ✅ Title max length validation
- ✅ Update operations

#### ProductSerializer Tests
- ✅ Basic serialization
- ✅ Read-only field handling

#### ProductDetailSerializer Tests
- ✅ Complete serialization with nested objects
- ✅ Option selectors method field

#### ProductListSerializer Tests
- ✅ List view serialization
- ✅ Nested variant serialization

#### ProductVariantSerializer Tests
- ✅ Price decimal handling
- ✅ Price validation
- ✅ Required field validation

#### ReviewSerializer Tests
- ✅ Complete serialization
- ✅ Rating validation (min/max)
- ✅ Valid rating range testing
- ✅ Required field validation
- ✅ Context handling

### 4. Business Logic Tests (`test_business_logic.py`)
**Priority: MEDIUM**

#### Category Business Logic
- ✅ Deep hierarchy creation
- ✅ Slug collision handling
- ✅ Parent change effects
- ✅ Deletion protection
- ✅ Deactivation vs deletion

#### Product Business Logic
- ✅ Rating calculation precision
- ✅ No active variants scenarios
- ✅ Slug uniqueness handling
- ✅ Complex category relationships

#### ProductVariant Business Logic
- ✅ Order field auto-assignment
- ✅ Stock management edge cases
- ✅ Price precision handling
- ✅ Weight validation edge cases
- ✅ Condition validation
- ✅ Cascade deletion

#### ProductImage Business Logic
- ✅ Order auto-assignment
- ✅ Manual reordering
- ✅ Deletion order gaps

#### Attribute Business Logic
- ✅ Complex PVAV validation
- ✅ Filtering flag usage

#### Review Business Logic
- ✅ Automatic rating updates
- ✅ Deletion rating updates
- ✅ Multiple reviews per customer

### 5. Performance Tests (`test_performance.py`)
**Priority: LOW**

#### Query Optimization
- ✅ Product list N+1 prevention
- ✅ Product detail optimization
- ✅ Category hierarchy efficiency

#### Concurrency Tests
- ✅ Concurrent review creation
- ✅ Concurrent variant order assignment

#### Data Integrity
- ✅ SKU uniqueness enforcement
- ✅ Category slug constraints
- ✅ PVAV uniqueness

#### Bulk Operations
- ✅ Bulk product creation
- ✅ Bulk review processing

#### Caching Tests
- ✅ Filter options caching
- ✅ Category tree optimization

## Django/DRF Testing Best Practices Applied

### 1. Fixture vs Factory Usage
- **✅ Model Bakery**: Used for creating test data with proper relationships
- **✅ Pytest Fixtures**: Used for reusable test setup (clients, users, common objects)
- **✅ Factory Pattern**: Applied in conftest.py for complex object creation

### 2. Authentication & Permission Testing
- **✅ Anonymous Access**: Tested for read operations
- **✅ Admin Access**: Tested for write operations
- **✅ Permission Classes**: Validated IsAdminOrReadOnly behavior
- **✅ Force Authentication**: Used for testing authenticated scenarios

### 3. Database Transaction Handling
- **✅ @pytest.mark.django_db**: Applied to all database tests
- **✅ Transaction Rollback**: Automatic cleanup between tests
- **✅ Isolation**: Each test runs in isolation

### 4. Mock Usage
- **✅ Minimal Mocking**: Real database operations for integration testing
- **✅ External Dependencies**: Would mock external APIs (Cloudinary, etc.)

### 5. Test Organization
- **✅ Descriptive Names**: Clear test method names describing scenarios
- **✅ AAA Pattern**: Arrange, Act, Assert structure
- **✅ Class Grouping**: Tests grouped by functionality
- **✅ Fixture Reuse**: Common setup in conftest.py

## Critical Tests to Prioritize (Immediate Action Items)

### 1. **Model Validation Tests** (Start Here)
```bash
pytest apps/products/tests/test_models.py -v
```
- These catch data integrity issues early
- Validate business rules at the model level

### 2. **API Authentication Tests** (High Priority)
```bash
pytest apps/products/tests/test_api_endpoints.py::TestCategoryViewSet::test_create_category_anonymous_user_returns_401 -v
```
- Critical for security
- Ensure proper permission enforcement

### 3. **Business Logic Tests** (Medium Priority)
```bash
pytest apps/products/tests/test_business_logic.py::TestProductBusinessLogic::test_product_average_rating_calculation_precision -v
```
- Validate complex calculations
- Ensure data consistency

### 4. **Serializer Validation Tests** (Medium Priority)
```bash
pytest apps/products/tests/test_serializers.py -v
```
- Catch API input/output issues
- Validate field constraints

## Running Tests

### Run All Product Tests
```bash
pytest apps/products/tests/ -v
```

### Run Specific Test Categories
```bash
# Model tests only
pytest apps/products/tests/test_models.py -v

# API tests only
pytest apps/products/tests/test_api_endpoints.py -v

# Business logic tests only
pytest apps/products/tests/test_business_logic.py -v
```

### Run with Coverage
```bash
pytest apps/products/tests/ --cov=apps.products --cov-report=html
```

## Next Steps

1. **Run the existing failing test** to ensure the permission fix works
2. **Implement model tests first** - they're foundational
3. **Add API endpoint tests** - they catch integration issues
4. **Gradually add business logic tests** - they validate complex scenarios
5. **Add performance tests last** - they optimize existing functionality

## Test Maintenance

- **Update tests when models change**
- **Add tests for new features**
- **Review test coverage regularly**
- **Refactor tests when they become brittle**
- **Keep fixtures up to date with model changes**
