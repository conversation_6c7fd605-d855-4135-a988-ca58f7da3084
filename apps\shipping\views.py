from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.core.cache import cache
import logging

from .models import Box, Carrier, CarrierService, PackingRule
from .serializers import (
    BoxSerializer, CarrierSerializer, CarrierServiceSerializer, PackingRuleSerializer,
    ShippingCalculationRequestSerializer, ShippingCalculationResponseSerializer,
    ShippingOptionsResponseSerializer, CarrierStatusSerializer
)
from .services import CartShippingService, PackingService, ShippingService
from apps.cart.models import Cart


logger = logging.getLogger(__name__)


class BoxViewSet(viewsets.ModelViewSet):
    """ViewSet for managing shipping boxes"""

    queryset = Box.objects.all()
    serializer_class = BoxSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter boxes based on query parameters"""
        queryset = Box.objects.all()

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        # Filter by mailer status
        is_mailer = self.request.query_params.get('is_mailer')
        if is_mailer is not None:
            queryset = queryset.filter(is_mailer=is_mailer.lower() == 'true')

        # Filter by volume range
        min_volume = self.request.query_params.get('min_volume')
        if min_volume:
            queryset = queryset.filter(volume__gte=min_volume)

        max_volume = self.request.query_params.get('max_volume')
        if max_volume:
            queryset = queryset.filter(volume__lte=max_volume)

        return queryset.order_by('volume', 'cost')

    def perform_create(self, serializer):
        """Create box and invalidate cache"""
        box = serializer.save()
        cache.delete('shipping:packing:boxes')
        logger.info(f"Box created: {box.title}")

    def perform_update(self, serializer):
        """Update box and invalidate cache"""
        box = serializer.save()
        cache.delete('shipping:packing:boxes')
        logger.info(f"Box updated: {box.title}")

    def perform_destroy(self, instance):
        """Soft delete box by setting is_active=False"""
        instance.is_active = False
        instance.save()
        cache.delete('shipping:packing:boxes')
        logger.info(f"Box deactivated: {instance.title}")

    @action(detail=True, methods=['post'])
    def test_fit(self, request, pk=None):
        """Test if items can fit in this box"""
        box = self.get_object()

        # Get test items from request
        items = request.data.get('items', [])

        results = []
        for item in items:
            length = item.get('length', 0)
            width = item.get('width', 0)
            height = item.get('height', 0)
            weight = item.get('weight', 0)

            can_fit_dimensions = box.can_fit_dimensions(length, width, height)
            can_fit_weight = box.can_fit_weight(weight)

            results.append({
                'item': item,
                'can_fit_dimensions': can_fit_dimensions,
                'can_fit_weight': can_fit_weight,
                'can_fit': can_fit_dimensions and can_fit_weight
            })

        return Response({
            'box': BoxSerializer(box).data,
            'test_results': results
        })


class CarrierViewSet(viewsets.ModelViewSet):
    """ViewSet for managing shipping carriers"""

    queryset = Carrier.objects.all()
    serializer_class = CarrierSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter carriers based on query parameters"""
        queryset = Carrier.objects.all()

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('-priority', 'title')

    @action(detail=True, methods=['post'])
    def test_connection(self, request, pk=None):
        """Test connection to carrier API"""
        carrier = self.get_object()

        try:
            from .services.carriers import get_carrier_instance
            carrier_instance = get_carrier_instance(carrier)

            # Test connection
            connection_result = carrier_instance.test_connection()

            return Response({
                'carrier_id': carrier.id,
                'carrier_name': carrier.title,
                'connection_status': 'success' if connection_result else 'failed',
                'message': 'Connection successful' if connection_result else 'Connection failed'
            })

        except Exception as e:
            logger.error(f"Carrier connection test failed for {carrier.title}: {e}")
            return Response({
                'carrier_id': carrier.id,
                'carrier_name': carrier.title,
                'connection_status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def status(self, request):
        """Get status of all carriers"""
        shipping_service = ShippingService()
        carrier_status = shipping_service.get_carrier_status()

        serializer = CarrierStatusSerializer(carrier_status, many=True)
        return Response(serializer.data)


class PackingRuleViewSet(viewsets.ModelViewSet):
    """ViewSet for managing packing rules"""

    queryset = PackingRule.objects.all()
    serializer_class = PackingRuleSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter rules based on query parameters"""
        queryset = PackingRule.objects.all()

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        # Filter by priority
        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)

        return queryset.order_by('-priority', 'title')

    def perform_create(self, serializer):
        """Create rule and invalidate cache"""
        rule = serializer.save()
        cache.delete('shipping:rules:active')
        logger.info(f"Packing rule created: {rule.title}")

    def perform_update(self, serializer):
        """Update rule and invalidate cache"""
        rule = serializer.save()
        cache.delete('shipping:rules:active')
        logger.info(f"Packing rule updated: {rule.title}")

    def perform_destroy(self, instance):
        """Delete rule and invalidate cache"""
        rule_title = instance.title
        instance.delete()
        cache.delete('shipping:rules:active')
        logger.info(f"Packing rule deleted: {rule_title}")

    @action(detail=True, methods=['post'])
    def test_rule(self, request, pk=None):
        """Test packing rule with sample data"""
        rule = self.get_object()

        # Get test parameters from request
        test_data = request.data
        total_weight = test_data.get('total_weight', 0)
        total_volume = test_data.get('total_volume', 0)
        item_count = test_data.get('item_count', 1)
        product_types = test_data.get('product_types', [])

        # Test if rule matches
        matches = rule.matches_conditions(total_weight, total_volume, item_count, product_types)

        # Calculate cost modification if matches
        base_cost = test_data.get('base_cost', 10.00)
        modified_cost = rule.apply_rule(base_cost) if matches else base_cost

        return Response({
            'rule': PackingRuleSerializer(rule).data,
            'test_parameters': test_data,
            'matches': matches,
            'base_cost': base_cost,
            'modified_cost': modified_cost,
            'cost_difference': modified_cost - base_cost
        })


class ShippingCalculationViewSet(viewsets.ViewSet):
    """ViewSet for shipping calculations"""

    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['post'])
    def calculate(self, request):
        """Calculate shipping cost for cart"""
        serializer = ShippingCalculationRequestSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        cart_id = serializer.validated_data['cart_id']
        force_recalculate = serializer.validated_data.get('force_recalculate', False)

        try:
            cart = get_object_or_404(Cart, id=cart_id)

            # Check if user owns the cart
            if cart.customer != request.user:
                return Response(
                    {'error': 'Cart not found or access denied'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Calculate shipping
            cart_shipping_service = CartShippingService()
            result = cart_shipping_service.recalculate_cart_shipping(cart, force_recalculate)

            response_serializer = ShippingCalculationResponseSerializer(data=result)
            if response_serializer.is_valid():
                return Response(response_serializer.data)
            else:
                return Response(result)

        except Exception as e:
            logger.error(f"Shipping calculation failed for cart {cart_id}: {e}")
            return Response({
                'success': False,
                'error': str(e),
                'message': 'Shipping calculation failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def options(self, request):
        """Get all shipping options for cart"""
        cart_id = request.data.get('cart_id')

        if not cart_id:
            return Response(
                {'error': 'cart_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            cart = get_object_or_404(Cart, id=cart_id)

            # Check if user owns the cart
            if cart.customer != request.user:
                return Response(
                    {'error': 'Cart not found or access denied'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get shipping options
            cart_shipping_service = CartShippingService()
            result = cart_shipping_service.get_shipping_options(cart)

            response_serializer = ShippingOptionsResponseSerializer(data=result)
            if response_serializer.is_valid():
                return Response(response_serializer.data)
            else:
                return Response(result)

        except Exception as e:
            logger.error(f"Failed to get shipping options for cart {cart_id}: {e}")
            return Response({
                'success': False,
                'error': str(e),
                'message': 'Failed to get shipping options'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def test_packing(self, request):
        """Test packing algorithm with custom items"""
        test_items = request.data.get('test_items', [])
        available_boxes = request.data.get('available_boxes', [])

        if not test_items:
            return Response(
                {'error': 'test_items is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Create mock cart items for testing
            from collections import namedtuple
            MockCartItem = namedtuple('MockCartItem', ['product', 'product_variant', 'quantity'])
            MockProduct = namedtuple('MockProduct', ['is_digital', 'title'])
            MockVariant = namedtuple('MockVariant', ['weight', 'length', 'width', 'height', 'volume', 'sku'])

            mock_cart_items = []
            for i, item in enumerate(test_items):
                mock_product = MockProduct(is_digital=False, title=f"Test Product {i+1}")
                mock_variant = MockVariant(
                    weight=item.get('weight', 100),
                    length=item.get('length', 10),
                    width=item.get('width', 10),
                    height=item.get('height', 10),
                    volume=item.get('length', 10) * item.get('width', 10) * item.get('height', 10),
                    sku=f"TEST-{i+1}"
                )
                mock_cart_item = MockCartItem(
                    product=mock_product,
                    product_variant=mock_variant,
                    quantity=item.get('quantity', 1)
                )
                mock_cart_items.append(mock_cart_item)

            # Run packing calculation
            packing_service = PackingService()
            packing_result = packing_service.calculate_optimal_packaging(mock_cart_items)

            # Format response
            from .serializers import PackingResultSerializer
            serializer = PackingResultSerializer(packing_result)

            return Response({
                'success': True,
                'test_items': test_items,
                'packing_result': serializer.data
            })

        except Exception as e:
            logger.error(f"Packing test failed: {e}")
            return Response({
                'success': False,
                'error': str(e),
                'message': 'Packing test failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def cache_status(self, request):
        """Get shipping cache status"""
        cache_keys = [
            'shipping:packing:boxes',
            'shipping:rules:active',
            'shipping:carriers:active'
        ]

        cache_status = {}
        for key in cache_keys:
            cached_data = cache.get(key)
            cache_status[key] = {
                'exists': cached_data is not None,
                'type': type(cached_data).__name__ if cached_data else None,
                'size': len(cached_data) if isinstance(cached_data, (list, dict)) else None
            }

        return Response({
            'cache_status': cache_status,
            'cache_backend': str(cache.__class__.__name__)
        })

    @action(detail=False, methods=['post'])
    def clear_cache(self, request):
        """Clear shipping-related cache"""
        cache_keys = [
            'shipping:packing:boxes',
            'shipping:rules:active',
            'shipping:carriers:active'
        ]

        cleared_keys = []
        for key in cache_keys:
            if cache.get(key) is not None:
                cache.delete(key)
                cleared_keys.append(key)

        # Also clear rate cache (pattern-based)
        try:
            rate_keys = cache.keys('shipping:rates:*')
            if rate_keys:
                cache.delete_many(rate_keys)
                cleared_keys.extend(rate_keys)
        except:
            # Some cache backends don't support keys() method
            pass

        return Response({
            'success': True,
            'message': f'Cleared {len(cleared_keys)} cache entries',
            'cleared_keys': cleared_keys
        })
