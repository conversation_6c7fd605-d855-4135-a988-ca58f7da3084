{% extends "admin/change_list.html" %}
{% load i18n admin_urls static %}

{% block content_title %}
    <h1>Enhanced Product Type Attribute Management</h1>
{% endblock %}

{% block extrahead %}{{ block.super }}
<style>
    .enhanced-info-container {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        border-radius: 4px;
        padding: 20px;
        margin: 20px 0;
    }
    
    .enhanced-info-container h3 {
        color: #0c5460;
        margin-top: 0;
    }
    
    .enhanced-info-container p {
        margin-bottom: 10px;
        color: #0c5460;
    }
    
    .enhanced-button {
        background-color: #007cba;
        border-color: #007cba;
        color: white;
        padding: 15px 30px;
        border-radius: 6px;
        text-decoration: none;
        display: inline-block;
        font-weight: bold;
        font-size: 16px;
        margin-top: 15px;
        transition: all 0.3s ease;
    }
    
    .enhanced-button:hover {
        background-color: #005a87;
        border-color: #005a87;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    .regular-admin-link {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        text-decoration: none;
        display: inline-block;
        margin-left: 15px;
    }
    
    .regular-admin-link:hover {
        background-color: #5a6268;
        border-color: #545b62;
        color: white;
        text-decoration: none;
    }
    
    .features-list {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
        margin: 15px 0;
    }
    
    .features-list li {
        margin: 8px 0;
        color: #495057;
    }
</style>
{% endblock %}

{% block content %}
<div class="enhanced-info-container">
    <h3>🚀 Enhanced Product Type Attribute Management</h3>
    <p>Experience our new and improved interface for managing product type attributes with advanced features:</p>
    
    <div class="features-list">
        <p><strong>✨ New Features:</strong></p>
        <ul>
            <li>🔍 <strong>Smart Autocomplete:</strong> Search and select attributes easily</li>
            <li>⚡ <strong>AJAX-Powered:</strong> Save individual associations without page refresh</li>
            <li>📝 <strong>Inline Editing:</strong> Edit existing associations directly</li>
            <li>🎯 <strong>Individual Settings:</strong> Set filterable/option selector per attribute</li>
            <li>📋 <strong>Context Aware:</strong> See existing associations while adding new ones</li>
            <li>🔄 <strong>Real-time Updates:</strong> Instant feedback and validation</li>
        </ul>
    </div>
    
    <div style="text-align: center;">
        <a href="{% url 'admin:products_enhanced_product_type_attribute_manage' %}" class="enhanced-button">
            🎯 Launch Enhanced Management Interface
        </a>
        
        <a href="{% url 'admin:products_producttypeattributeproxy_changelist' %}" class="regular-admin-link">
            📋 View Traditional List
        </a>
    </div>
</div>

<script>
// Auto-redirect to enhanced interface after 3 seconds if user doesn't interact
let redirectTimer = setTimeout(function() {
    if (confirm('🚀 Would you like to try the new enhanced interface? It offers much better functionality!')) {
        window.location.href = "{% url 'admin:products_enhanced_product_type_attribute_manage' %}";
    }
}, 3000);

// Cancel auto-redirect if user interacts with the page
document.addEventListener('click', function() {
    clearTimeout(redirectTimer);
});

document.addEventListener('keydown', function() {
    clearTimeout(redirectTimer);
});
</script>
{% endblock %}
