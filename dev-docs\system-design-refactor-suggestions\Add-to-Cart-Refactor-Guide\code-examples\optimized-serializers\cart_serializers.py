# Optimized Cart Serializers
# This file contains optimized versions of cart serializers with proper prefetching and caching

from rest_framework import serializers
from rest_framework.serializers import ModelSerializer
from django.db.models import Prefetch, F, Sum
from django.core.cache import cache
from decimal import Decimal
from apps.cart.models import Cart, CartItem
from apps.products.models import ProductVariant, Discount, AttributeValue
from apps.cart.services.cache import CartCacheService
from apps.cart.tasks import calculate_shipping_async, update_cart_totals_async
import hashlib
import json


class OptimizedCartItemSerializer(ModelSerializer):
    """Optimized cart item serializer with prefetched data usage"""
    
    total_price = serializers.SerializerMethodField()
    product_title = serializers.CharField(source='product.title', read_only=True)
    product_brand = serializers.CharField(source='product.brand.name', read_only=True)
    variant_sku = serializers.CharField(source='product_variant.sku', read_only=True)
    variant_price = serializers.DecimalField(source='product_variant.price', max_digits=6, decimal_places=2, read_only=True)
    discounted_price = serializers.SerializerMethodField()
    product_image = serializers.SerializerMethodField()
    stock_available = serializers.IntegerField(source='product_variant.stock_qty', read_only=True)
    weight = serializers.DecimalField(source='product_variant.weight', max_digits=8, decimal_places=2, read_only=True)

    def get_total_price(self, obj):
        """Calculate total price using prefetched discount data"""
        # Use prefetched active_discounts to avoid additional queries
        active_discounts = getattr(obj.product_variant, 'active_discounts', [])
        if active_discounts:
            discounted_price = active_discounts[0].apply_discount(obj.product_variant.price)
        else:
            discounted_price = obj.product_variant.price
        
        return Decimal(str(discounted_price)) * obj.quantity

    def get_discounted_price(self, obj):
        """Get discounted price using prefetched data"""
        active_discounts = getattr(obj.product_variant, 'active_discounts', [])
        if active_discounts:
            return active_discounts[0].apply_discount(obj.product_variant.price)
        return obj.product_variant.price

    def get_product_image(self, obj):
        """Get product image using prefetched data"""
        images = getattr(obj.product_variant, 'product_image', None)
        if images and hasattr(images, 'all'):
            first_image = images.first()
            if first_image:
                return {
                    'url': first_image.image.url if first_image.image else None,
                    'alt_text': first_image.alt_text or obj.product.title
                }
        return None

    class Meta:
        model = CartItem
        fields = [
            'id', 'product_title', 'product_brand', 'variant_sku', 
            'variant_price', 'discounted_price', 'quantity', 'total_price',
            'product_image', 'stock_available', 'weight', 'created_at', 'updated_at'
        ]


class OptimizedCartSerializer(ModelSerializer):
    """Optimized cart serializer with caching and async operations"""
    
    cart_items = OptimizedCartItemSerializer(many=True, read_only=True)
    total_price = serializers.SerializerMethodField()
    total_weight = serializers.SerializerMethodField()
    total_items = serializers.SerializerMethodField()
    shipping_status = serializers.SerializerMethodField()
    cache_status = serializers.SerializerMethodField()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_service = CartCacheService()

    def get_total_price(self, obj):
        """Calculate total price using database aggregation when possible"""
        # Try to get from cache first
        cache_key = f"cart_total_price:{obj.id}"
        cached_total = cache.get(cache_key)
        
        if cached_total is not None:
            return cached_total
        
        # Calculate using prefetched data
        total = Decimal('0.00')
        for item in obj.cart_items.all():
            active_discounts = getattr(item.product_variant, 'active_discounts', [])
            if active_discounts:
                price = active_discounts[0].apply_discount(item.product_variant.price)
            else:
                price = item.product_variant.price
            total += Decimal(str(price)) * item.quantity
        
        # Cache the result for 5 minutes
        cache.set(cache_key, total, 300)
        return total

    def get_total_weight(self, obj):
        """Get total weight using database aggregation"""
        # Use the optimized method from the model
        return obj.get_cart_weight()

    def get_total_items(self, obj):
        """Get total number of items"""
        return sum(item.quantity for item in obj.cart_items.all())

    def get_shipping_status(self, obj):
        """Get shipping calculation status"""
        cache_key = f"shipping_calculation:{obj.id}"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            return {
                'status': 'completed',
                'last_calculated': obj.last_shipping_calculation,
                'shipping_cost': obj.shipping_cost,
                'packing_cost': obj.packing_cost
            }
        
        return {
            'status': 'pending' if obj.last_shipping_calculation else 'not_calculated',
            'last_calculated': obj.last_shipping_calculation,
            'shipping_cost': obj.shipping_cost,
            'packing_cost': obj.packing_cost
        }

    def get_cache_status(self, obj):
        """Get cache status for debugging"""
        return {
            'cart_cached': self.cache_service.get_cached_cart_data(obj.id) is not None,
            'shipping_cached': cache.get(f"shipping_calculation:{obj.id}") is not None,
            'packing_cached': cache.get(f"packing_result:{obj.id}") is not None
        }

    class Meta:
        model = Cart
        fields = [
            'id', 'customer', 'cart_items', 'total_price', 'total_weight', 
            'total_items', 'shipping_cost', 'packing_cost', 'total_volume',
            'shipping_status', 'cache_status', 'created_at', 'last_shipping_calculation'
        ]


class AsyncAddCartItemSerializer(ModelSerializer):
    """Async cart item serializer for high-performance operations"""
    
    product_id = serializers.IntegerField()
    product_variant = serializers.PrimaryKeyRelatedField(queryset=ProductVariant.objects.all())
    extra_data = serializers.JSONField(default=dict)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_service = CartCacheService()

    def validate_product_id(self, value):
        """Validate product exists and is active"""
        from apps.products.models import Product
        
        # Use cache for product validation
        cache_key = f"product_active:{value}"
        is_active = cache.get(cache_key)
        
        if is_active is None:
            try:
                product = Product.objects.get(pk=value, is_active=True)
                cache.set(cache_key, True, 3600)  # Cache for 1 hour
                is_active = True
            except Product.DoesNotExist:
                cache.set(cache_key, False, 300)  # Cache negative result for 5 minutes
                is_active = False
        
        if not is_active:
            raise serializers.ValidationError('Product not found or inactive')
        return value

    def validate(self, data):
        """Validate cart weight and stock availability"""
        cart_id = self.context['cart_id']
        product_variant = data['product_variant']
        quantity = data['quantity']

        # Check stock availability
        if product_variant.stock_qty < quantity:
            raise serializers.ValidationError(
                f"Insufficient stock. Available: {product_variant.stock_qty}, Requested: {quantity}"
            )

        # Check cart weight limit using cached cart data
        cached_cart_data = self.cache_service.get_cached_cart_data(cart_id)
        if cached_cart_data:
            current_weight = cached_cart_data.get('total_weight', 0)
        else:
            # Fallback to database query
            try:
                cart = Cart.objects.get(id=cart_id)
                current_weight = float(cart.get_cart_weight())
            except Cart.DoesNotExist:
                current_weight = 0

        additional_weight = float(product_variant.weight) * quantity
        if current_weight + additional_weight > 20000:  # 20kg limit
            raise serializers.ValidationError(
                f"Adding this item would exceed the maximum weight limit of 20,000 grams. "
                f"Current: {current_weight}g, Additional: {additional_weight}g"
            )

        return data

    def save(self, **kwargs):
        """Save with async processing for better performance"""
        cart_id = self.context['cart_id']
        product_variant = self.validated_data['product_variant']
        quantity = self.validated_data['quantity']
        extra_data = self.validated_data.get('extra_data', {})

        # Invalidate cart cache before modification
        self.cache_service.invalidate_cart_cache(cart_id)

        # Get or create cart
        try:
            cart = Cart.objects.get(id=cart_id)
        except Cart.DoesNotExist:
            cart = Cart.objects.create(id=cart_id)

        # Calculate price to use (with caching)
        cache_key = f"variant_price:{product_variant.id}"
        cached_price = cache.get(cache_key)
        
        if cached_price is None:
            price_to_use = product_variant.get_discounted_price() or product_variant.price
            cache.set(cache_key, float(price_to_use), 1800)  # Cache for 30 minutes
        else:
            price_to_use = Decimal(str(cached_price))

        # Update or create cart item
        cart_item, created = CartItem.objects.get_or_create(
            cart=cart,
            product_id=self.validated_data['product_id'],
            product_variant=product_variant,
            defaults={
                'quantity': quantity,
                'extra_data': extra_data
            }
        )

        if not created:
            cart_item.quantity += quantity
            cart_item.save(update_fields=['quantity', 'updated_at'])

        # Trigger async operations for better performance
        update_cart_totals_async.delay(cart_id)
        calculate_shipping_async.delay(cart_id, force_recalculate=True)

        # Cache updated cart data optimistically
        self.cache_service.cache_cart_data(cart)

        self.instance = cart_item
        return cart_item

    class Meta:
        model = CartItem
        fields = ['product_id', 'product_variant', 'quantity', 'extra_data']


class BulkCartUpdateSerializer(serializers.Serializer):
    """Serializer for bulk cart operations"""
    
    operations = serializers.ListField(
        child=serializers.DictField(),
        max_length=50  # Limit bulk operations
    )

    def validate_operations(self, value):
        """Validate bulk operations"""
        valid_operations = ['add', 'update', 'remove']
        
        for operation in value:
            if 'type' not in operation or operation['type'] not in valid_operations:
                raise serializers.ValidationError(
                    f"Invalid operation type. Must be one of: {valid_operations}"
                )
            
            if operation['type'] in ['add', 'update'] and 'quantity' not in operation:
                raise serializers.ValidationError("Quantity required for add/update operations")
            
            if 'product_variant_id' not in operation:
                raise serializers.ValidationError("product_variant_id required for all operations")

        return value

    def save(self, **kwargs):
        """Execute bulk operations efficiently"""
        cart_id = self.context['cart_id']
        operations = self.validated_data['operations']
        
        # Invalidate cache before bulk operations
        cache_service = CartCacheService()
        cache_service.invalidate_cart_cache(cart_id)
        
        results = []
        
        # Process operations in batches for better performance
        from django.db import transaction
        
        with transaction.atomic():
            cart, created = Cart.objects.get_or_create(id=cart_id)
            
            for operation in operations:
                try:
                    result = self._process_single_operation(cart, operation)
                    results.append(result)
                except Exception as e:
                    results.append({
                        'success': False,
                        'operation': operation,
                        'error': str(e)
                    })
        
        # Trigger async updates after all operations
        update_cart_totals_async.delay(cart_id)
        calculate_shipping_async.delay(cart_id, force_recalculate=True)
        
        return {
            'cart_id': str(cart_id),
            'operations_processed': len(operations),
            'results': results
        }

    def _process_single_operation(self, cart, operation):
        """Process a single bulk operation"""
        op_type = operation['type']
        variant_id = operation['product_variant_id']
        
        if op_type == 'add':
            quantity = operation['quantity']
            cart_item, created = CartItem.objects.get_or_create(
                cart=cart,
                product_variant_id=variant_id,
                defaults={'quantity': quantity}
            )
            if not created:
                cart_item.quantity += quantity
                cart_item.save()
            
            return {
                'success': True,
                'operation': operation,
                'cart_item_id': cart_item.id,
                'new_quantity': cart_item.quantity
            }
        
        elif op_type == 'update':
            quantity = operation['quantity']
            cart_item = CartItem.objects.get(cart=cart, product_variant_id=variant_id)
            cart_item.quantity = quantity
            cart_item.save()
            
            return {
                'success': True,
                'operation': operation,
                'cart_item_id': cart_item.id,
                'new_quantity': cart_item.quantity
            }
        
        elif op_type == 'remove':
            CartItem.objects.filter(cart=cart, product_variant_id=variant_id).delete()
            
            return {
                'success': True,
                'operation': operation,
                'removed': True
            }


# Usage example in views:
"""
# In your CartViewSet:

def get_queryset(self):
    return Cart.objects.select_related('customer').prefetch_related(
        Prefetch(
            'cart_items',
            queryset=CartItem.objects.select_related(
                'product',
                'product_variant',
                'product__brand',
                'product__category'
            ).prefetch_related(
                'product_variant__product_image',
                Prefetch(
                    'product_variant__discounts',
                    queryset=Discount.objects.filter(
                        is_active=True,
                        start_date__lte=timezone.now(),
                        end_date__gte=timezone.now()
                    ),
                    to_attr='active_discounts'
                )
            )
        )
    )

def get_serializer_class(self):
    if self.action == 'create_item':
        return AsyncAddCartItemSerializer
    elif self.action == 'bulk_update':
        return BulkCartUpdateSerializer
    return OptimizedCartSerializer
"""
