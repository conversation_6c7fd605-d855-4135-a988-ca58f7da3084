# Refactor Plan for Django E-Commerce Platform

## Design Overview

### Current Architecture

The current Django project is organized into two main apps:

- **Core App**: Manages user authentication.
- **Store App**: Handles products, shopping carts, orders, and payments.

### Pitfalls of the Current Architecture

- **Monolithic Store App**: The Store app handles too many responsibilities, leading to complexity and difficulty in
  maintenance.
- **Tight Coupling**: The Order and Cart models heavily depend on `Product` and `ProductVariant`. While this is expected
  in an e-commerce context, the tight coupling makes the system less flexible.
- **Scalability Concerns**: As features like promotions, advanced cart functionality, and custom order workflows are
  added, the monolithic structure may become difficult to manage.
- **Separation of Concerns**: Business logic for products, cart, orders, and payments is mixed, violating the Single
  Responsibility Principle (SRP).

## Refactor Plan

### Objective

To improve readability, modularity, and maintainability, we will split the Store app into smaller, more focused apps.
This will allow independent development and testing of different domains of the e-commerce platform.

### Proposed App Structure

- **Core App**: Authentication and user management (no change).
- **Catalog App**: Product management.
- **Cart App**: Shopping cart and cart items management.
- **Order App**: Order creation and tracking.
- **Payments App**: Payment processing (refactored).
- **Customers App**: Customer profiles and addresses.

## App Breakdown

### 1. Catalog App

**Models**

- `Product`, `ProductVariant`, `Category`, `Brand`, `ProductType`, `Attribute`, `AttributeValue`, `Review`.

**Serializers**

- `ProductSerializer`, `ProductVariantSerializer`, `CategorySerializer`, `AttributeSerializer`, `AttributeValueSerializer`, `ReviewSerializer`.

**Views**

- `ProductViewSet`, `CategoryViewSet`, `ReviewViewSet`.

**Logical Reasoning**:

- **High Cohesion**: The product catalog is a well-defined domain. All product-related logic (e.g., product variants,
  attributes, reviews) should be grouped together.
- **Reusability**: The Catalog app could be reused in a system that does not need shopping cart or order logic, such as
  a product showcase or inventory management platform.

### 2. Cart App

**Models**

- `Cart`, `CartItem`.

**Serializers**

- `CartSerializer`, `CartItemSerializer`, `AddCartItemSerializer`, `UpdateCartItemSerializer`.

**Views**

- `CartViewSet`, `CartItemViewSet`.

**Logical Reasoning**:

- **Single Responsibility**: The cart functionality should be isolated from product and order management. This
  separation allows for independent development and testing of cart-specific logic, such as persistent carts,
  promotions, and abandoned cart reminders.
- **Loose Coupling**: While the cart depends on products (via `ProductVariant`), keeping it separate from product
  management allows changes in cart behavior without affecting product logic.

### 3. Order App

**Models**

- `Order`, `OrderItem`, `Address`.

**Serializers**

- `OrderSerializer`, `OrderItemSerializer`, `CreateOrderSerializer`, `AddressSerializer`, `UpdateOrderDeliveryStatusSerializer`.

**Views**

- `OrderViewSet`, `AddressViewSet`.

**Logical Reasoning**:

- **Order Workflow**: Order processing is a distinct business domain involving payment, shipping, and delivery. By
  separating it, you can add order-specific features like order tracking, shipping integration, and delivery
  notifications.
- **Scalability**: Orders often have complex workflows. Keeping the order logic isolated ensures scalability as new
  order-related features are introduced.

### 4. Payments App (Refactored)

**Models**

- `PaymentOption`.

**Serializers**

- `PaymentOptionSerializer`.

**Views**

- `PaymentOptionViewSet`, `StripeCheckoutView`, `StripeWebhookView`.

**Logical Reasoning**:

- **Modularity**: Payment logic frequently evolves (e.g., adding new payment gateways). By isolating payments, you can
  introduce new providers or adjust payment workflows without affecting other parts of the system.
- **Loose Coupling**: Payments are loosely coupled with the order system. This separation allows the payment logic to be
  updated independently from order management.

### 5. Customers App

**Models**

- `Customer`, `Address`.

**Serializers**

- `CustomerSerializer`, `SimpleCustomerSerializer`, `DetailedCustomerSerializer`, `AddressSerializer`.

**Views**

- `CustomerViewSet`, `AddressViewSet`.

**Logical Reasoning**:

- **Single Responsibility**: Managing customer data (e.g., profiles, addresses) is a distinct domain from orders or
  carts. Separating this logic ensures the customer module can evolve independently (e.g., implementing loyalty programs
  or account management features).
- **Data Ownership**: By having a separate app, customer information is isolated, which helps in implementing security
  or data privacy policies more easily.

## Rationale

1. **Improved Readability and Maintainability**  
   By splitting the monolithic Store app, each app will focus on a single domain, making the codebase easier to
   understand and maintain. This also helps isolate responsibilities, which is key to reducing bugs and improving the
   ease of making future changes.

2. **Modularity and Reusability**  
   Each app can be reused across different contexts. For example, the Catalog App could serve as a product catalog in
   another system, or the Payments App could be reused in a completely different project needing Stripe integration.

3. **Scalability**  
   The new structure allows each app to scale independently. For instance, if the cart functionality needs more advanced
   features like discount codes or cross-device persistence, it can evolve without affecting the product or order
   management systems.

4. **Lower Coupling and High Cohesion**  
   While some dependencies are unavoidable (e.g., orders depend on products), the refactored structure minimizes
   unnecessary coupling. The apps remain cohesive, each focusing on a well-defined domain, reducing the chances of
   cascading changes.
