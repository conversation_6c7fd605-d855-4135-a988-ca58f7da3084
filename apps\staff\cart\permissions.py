from rest_framework.permissions import BasePermission
from apps.staff.authorization.permissions import CanAccessStaffAPI
from apps.staff.common.constants import STAFF_GROUPS


class CanManageCarts(BasePermission):
    """
    Permission class for cart management operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        # Check basic staff API access
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check specific cart permissions based on action
        if hasattr(view, 'action'):
            action = view.action
            
            if action in ['list', 'retrieve']:
                return request.user.has_perm('cart.view_all_carts')
            elif action in ['update', 'partial_update']:
                return request.user.has_perm('cart.modify_cart_items')
            elif action in ['bulk_operations', 'abandoned_carts']:
                return request.user.has_perm('cart.bulk_cart_operations')
            elif action == 'analytics':
                return request.user.has_perm('cart.cart_analytics')
        
        return request.user.has_perm('cart.view_all_carts')


class CanViewCartAnalytics(BasePermission):
    """
    Permission class for cart analytics operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check if user has analytics permissions
        user_groups = set(request.user.groups.values_list('name', flat=True))
        allowed_groups = {
            STAFF_GROUPS['CUSTOMER_SERVICE_MANAGER'],
            STAFF_GROUPS['CUSTOMER_SERVICE_REPRESENTATIVE'],
            STAFF_GROUPS['ORDER_MANAGER'],
            STAFF_GROUPS['SYSTEM_ADMINISTRATOR']
        }
        
        return (
            request.user.is_superuser or
            bool(user_groups.intersection(allowed_groups)) or
            request.user.has_perm('cart.cart_analytics')
        )


class CanManageAbandonedCarts(BasePermission):
    """
    Permission class for abandoned cart management
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check if user has abandoned cart management permissions
        user_groups = set(request.user.groups.values_list('name', flat=True))
        allowed_groups = {
            STAFF_GROUPS['CUSTOMER_SERVICE_MANAGER'],
            STAFF_GROUPS['CUSTOMER_SERVICE_REPRESENTATIVE'],
            STAFF_GROUPS['MARKETING_MANAGER']
        }
        
        return (
            request.user.is_superuser or
            bool(user_groups.intersection(allowed_groups)) or
            request.user.has_perm('cart.manage_abandoned_carts')
        )
