# Test models - validation, custom methods, constraints
import pytest
from decimal import Decimal
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from model_bakery import baker
from apps.products.models import (
    Category, Product, ProductVariant, ProductImage, Review,
    ProductVariantAttributeValue, Attribute, AttributeValue,
    ProductType, Brand
)
from apps.customers.models import Customer


@pytest.mark.django_db
class TestCategoryModel:
    """Test Category model validation and custom methods"""
    
    def test_category_str_method_without_parent(self):
        """Test __str__ method for root category"""
        category = baker.make(Category, title="Electronics")
        assert str(category) == "Electronics"
    
    def test_category_str_method_with_parent(self):
        """Test __str__ method for nested category"""
        parent = baker.make(Category, title="Electronics")
        child = baker.make(Category, title="Laptops", parent=parent)
        assert str(child) == "Electronics > Laptops"
    
    def test_category_slug_auto_generation_on_create(self):
        """Test slug is auto-generated from title on creation"""
        category = Category.objects.create(title="Hard Drives")
        assert category.slug == "hard-drives"
    
    def test_category_slug_with_parent_concatenation(self):
        """Test slug concatenation with parent slug"""
        parent = Category.objects.create(title="Storage", slug="storage")
        child = Category.objects.create(title="Hard Drives", parent=parent)
        assert child.slug == "storage-hard-drives"
    
    def test_category_unique_together_constraint(self):
        """Test unique_together constraint on (slug, parent)"""
        parent = baker.make(Category)
        baker.make(Category, slug="test-slug", parent=parent)
        
        with pytest.raises(IntegrityError):
            Category.objects.create(slug="test-slug", parent=parent, title="Duplicate")
    
    def test_category_slug_update_behavior(self):
        """Test slug behavior on update"""
        category = Category.objects.create(title="Original Title")
        original_slug = category.slug
        
        category.title = "Updated Title"
        category.save()
        
        # Slug should remain the same on update unless explicitly changed
        assert category.slug == original_slug


@pytest.mark.django_db
class TestProductModel:
    """Test Product model validation and custom methods"""
    
    def test_product_str_method(self):
        """Test Product __str__ method"""
        product = baker.make(Product, title="Gaming Laptop")
        assert str(product) == "Gaming Laptop"
    
    def test_product_update_average_rating_with_reviews(self):
        """Test update_average_rating method with multiple reviews"""
        product = baker.make(Product)
        customer1 = baker.make(Customer)
        customer2 = baker.make(Customer)
        
        # Create reviews with different ratings
        baker.make(Review, product=product, customer=customer1, rating=4)
        baker.make(Review, product=product, customer=customer2, rating=5)
        
        product.update_average_rating()
        assert product.average_rating == 4.5
    
    def test_product_update_average_rating_no_reviews(self):
        """Test update_average_rating method with no reviews"""
        product = baker.make(Product, average_rating=3.0)
        product.update_average_rating()
        assert product.average_rating == 0.0
    
    def test_product_required_fields(self):
        """Test that required fields are enforced"""
        with pytest.raises(IntegrityError):
            Product.objects.create(title="Test Product")  # Missing required ForeignKey fields


@pytest.mark.django_db
class TestProductVariantModel:
    """Test ProductVariant model validation and custom methods"""
    
    def test_product_variant_str_method(self):
        """Test ProductVariant __str__ method"""
        product = baker.make(Product, title="Gaming Laptop")
        variant = baker.make(ProductVariant, product=product, price=Decimal('999.99'))
        assert str(variant) == "Gaming Laptop - 999.99 variant"
    
    def test_product_variant_price_validation_minimum(self):
        """Test price minimum validation"""
        product = baker.make(Product)
        with pytest.raises(ValidationError):
            variant = ProductVariant(
                product=product,
                price=Decimal('0.50'),  # Below minimum of 1
                sku="TEST-SKU",
                stock_qty=10,
                weight=Decimal('100.00')
            )
            variant.full_clean()
    
    def test_product_variant_weight_validation_minimum(self):
        """Test weight minimum validation"""
        product = baker.make(Product)
        with pytest.raises(ValidationError):
            variant = ProductVariant(
                product=product,
                price=Decimal('99.99'),
                sku="TEST-SKU",
                stock_qty=10,
                weight=Decimal('-1.00')  # Negative weight
            )
            variant.full_clean()
    
    def test_product_variant_sku_uniqueness(self):
        """Test SKU uniqueness constraint"""
        baker.make(ProductVariant, sku="UNIQUE-SKU")
        
        with pytest.raises(IntegrityError):
            baker.make(ProductVariant, sku="UNIQUE-SKU")
    
    def test_product_variant_order_field_duplicate_validation(self):
        """Test order field duplicate validation within same product"""
        product = baker.make(Product)
        baker.make(ProductVariant, product=product, order=1)
        
        with pytest.raises(ValidationError):
            variant = ProductVariant(
                product=product,
                order=1,  # Duplicate order
                price=Decimal('99.99'),
                sku="TEST-SKU-2",
                stock_qty=10,
                weight=Decimal('100.00')
            )
            variant.save()  # This calls full_clean() which should raise ValidationError
    
    def test_product_variant_condition_choices(self):
        """Test condition field choices"""
        product = baker.make(Product)
        
        # Valid condition
        variant = baker.make(ProductVariant, product=product, condition='New')
        assert variant.condition == 'New'
        
        # Invalid condition should be caught by model validation
        with pytest.raises(ValidationError):
            variant = ProductVariant(
                product=product,
                condition='Invalid',
                price=Decimal('99.99'),
                sku="TEST-SKU",
                stock_qty=10,
                weight=Decimal('100.00')
            )
            variant.full_clean()


@pytest.mark.django_db
class TestProductImageModel:
    """Test ProductImage model validation and custom methods"""
    
    def test_product_image_str_method(self):
        """Test ProductImage __str__ method"""
        variant = baker.make(ProductVariant, sku="TEST-SKU")
        image = baker.make(ProductImage, product_variant=variant)
        assert str(image) == "TEST-SKU_img"
    
    def test_product_image_order_duplicate_validation(self):
        """Test order field duplicate validation within same product variant"""
        variant = baker.make(ProductVariant)
        baker.make(ProductImage, product_variant=variant, order=1)
        
        with pytest.raises(ValidationError):
            image = ProductImage(
                product_variant=variant,
                order=1,  # Duplicate order
                alternative_text="Test image",
                image="test.jpg"
            )
            image.save()


@pytest.mark.django_db
class TestReviewModel:
    """Test Review model validation and custom methods"""
    
    def test_review_str_method(self):
        """Test Review __str__ method"""
        review = baker.make(Review, title="Great product!")
        assert str(review) == "Great product!"
    
    def test_review_rating_validation_minimum(self):
        """Test rating minimum validation"""
        with pytest.raises(ValidationError):
            review = Review(
                title="Bad review",
                rating=0,  # Below minimum of 1
                description="Test description",
                product=baker.make(Product),
                customer=baker.make(Customer)
            )
            review.full_clean()
    
    def test_review_rating_validation_maximum(self):
        """Test rating maximum validation"""
        with pytest.raises(ValidationError):
            review = Review(
                title="Impossible review",
                rating=6,  # Above maximum of 5
                description="Test description",
                product=baker.make(Product),
                customer=baker.make(Customer)
            )
            review.full_clean()
    
    def test_review_updates_product_average_rating_on_save(self):
        """Test that saving a review updates product's average rating"""
        product = baker.make(Product, average_rating=0.0)
        customer = baker.make(Customer)
        
        Review.objects.create(
            title="Great product",
            rating=5,
            description="Love it!",
            product=product,
            customer=customer
        )
        
        product.refresh_from_db()
        assert product.average_rating == 5.0


@pytest.mark.django_db
class TestProductVariantAttributeValueModel:
    """Test ProductVariantAttributeValue model validation and custom methods"""
    
    def test_pvav_str_method(self):
        """Test ProductVariantAttributeValue __str__ method"""
        attribute = baker.make(Attribute, title="Color")
        attr_value = baker.make(AttributeValue, attribute=attribute, attribute_value="Red")
        variant = baker.make(ProductVariant)
        
        pvav = baker.make(
            ProductVariantAttributeValue,
            attribute_value=attr_value,
            product_variant=variant
        )
        
        expected_str = f'"{attr_value}" is an attribute value of "{variant}"'
        assert str(pvav) == expected_str
    
    def test_pvav_unique_together_constraint(self):
        """Test unique_together constraint on (attribute_value, product_variant)"""
        attr_value = baker.make(AttributeValue)
        variant = baker.make(ProductVariant)
        
        baker.make(ProductVariantAttributeValue, 
                  attribute_value=attr_value, 
                  product_variant=variant)
        
        with pytest.raises(IntegrityError):
            ProductVariantAttributeValue.objects.create(
                attribute_value=attr_value,
                product_variant=variant
            )
    
    def test_pvav_duplicate_attribute_validation(self):
        """Test validation prevents duplicate attributes for same variant"""
        # Create attribute and two values for it
        attribute = baker.make(Attribute, title="Color")
        red_value = baker.make(AttributeValue, attribute=attribute, attribute_value="Red")
        blue_value = baker.make(AttributeValue, attribute=attribute, attribute_value="Blue")
        variant = baker.make(ProductVariant)
        
        # First attribute value should save fine
        baker.make(ProductVariantAttributeValue, 
                  attribute_value=red_value, 
                  product_variant=variant)
        
        # Second attribute value of same type should raise ValidationError
        with pytest.raises(ValidationError):
            pvav = ProductVariantAttributeValue(
                attribute_value=blue_value,
                product_variant=variant
            )
            pvav.save()  # This calls full_clean() which should raise ValidationError
