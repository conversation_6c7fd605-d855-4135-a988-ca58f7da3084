# Generated migration for selective checkout feature

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cart', '0003_cart_packing_details'),
    ]

    operations = [
        migrations.AddField(
            model_name='cartitem',
            name='is_selected',
            field=models.BooleanField(default=True, help_text='Whether this item is selected for checkout'),
        ),
        migrations.AddField(
            model_name='cartitem',
            name='selected_at',
            field=models.DateTimeField(blank=True, help_text='Timestamp when item was last selected/deselected', null=True),
        ),
        migrations.AddIndex(
            model_name='cartitem',
            index=models.Index(fields=['cart', 'is_selected'], name='cart_cartitem_cart_is_selected_idx'),
        ),
        migrations.AddIndex(
            model_name='cartitem',
            index=models.Index(fields=['is_selected', 'selected_at'], name='cart_cartitem_is_selected_selected_at_idx'),
        ),
    ]
