# Product Management API Migration Plan

## **Overview**

This document outlines the comprehensive plan for migrating all product-related Django admin functionalities to API-first approach within the staff app. The goal is to enable staff members with appropriate roles to perform all product management operations via REST APIs that will be consumed by a React Admin frontend.

## **Current State Analysis**

### **Existing Product Models**

- **Category**: MPTT-based hierarchical categories with slug generation
- **Product**: Main product entity with brand, category, product type relationships
- **ProductVariant**: Product variations with pricing, SKU, stock, and ordering
- **ProductType**: Product classification with attribute associations
- **Attribute**: Product characteristics (e.g., Color, Size, Material)
- **AttributeValue**: Specific values for attributes (e.g., Red, Large, Cotton)
- **ProductTypeAttribute**: Many-to-many through model with filterable/option selector flags
- **ProductAttributeValue**: Product-level attribute associations
- **ProductVariantAttributeValue**: Variant-level attribute associations
- **Brand**: Product brands with product type associations
- **BrandProductType**: Brand-product type relationships
- **ProductImage**: Product variant images with ordering
- **Review**: Customer product reviews
- **Discount**: Product variant discounts with date ranges

### **Association Models Implemented**
- **BrandProductType**: Associates brands with product types they can manufacture
- **ProductTypeAttribute**: Associates attributes with product types (with filterable/option flags)
- **ProductAttributeValue**: Associates attribute values directly with products
- **ProductVariantAttributeValue**: Associates attribute values with specific product variants
- **All association models**: Full CRUD operations with bulk management capabilities

### **Current Django Admin Features**

- **Enhanced Product Type Attribute Management**: AJAX-powered bulk association interface
- **Enhanced Attribute Value Management**: AJAX-powered bulk value management
- **Hierarchical Category Management**: Draggable MPTT admin with product counts
- **Product Variant Ordering**: Drag-and-drop reordering within products
- **Image Management**: Inline image upload with ordering
- **Bulk Operations**: Custom admin interfaces for bulk associations
- **Search and Filtering**: Advanced filtering across all models
- **Autocomplete Fields**: For efficient relationship selection
- **Read-only Review Management**: View-only access to customer reviews

### **Existing API Endpoints (Read-Only)**

- `GET /api/products/` - Product listing with filtering
- `GET /api/products/{slug}/` - Product detail
- `GET /api/products/categories/` - Category listing
- `GET /api/products/product-types/` - Product type attributes
- `GET /api/products/product-filter-options/` - Filter options
- `GET /api/products/{slug}/reviews/` - Product reviews

## **Target Architecture**

### **Staff App Structure**

```
apps/staff/products/
├── models.py          # Proxy models and staff-specific models
├── serializers.py     # Staff API serializers
├── views.py          # Staff API viewsets
├── permissions.py    # Product-specific permissions
├── services.py       # Business logic services
├── filters.py        # API filtering classes
├── urls.py           # Staff product API URLs
└── admin.py          # Minimal admin for debugging
```

### **API URL Structure**

```
/api/staff/products/                    # Product CRUD
/api/staff/products/bulk/               # Bulk operations
/api/staff/products/categories/         # Category management
/api/staff/products/brands/             # Brand management
/api/staff/products/product-types/      # Product type management
/api/staff/products/attributes/         # Attribute management
/api/staff/products/attribute-values/   # Attribute value management
/api/staff/products/associations/       # Bulk association management
/api/staff/products/reviews/            # Review management
/api/staff/products/discounts/          # Discount management
```

## **Implementation Phases**

### **Phase 1: Foundation Setup (Week 1)**

#### **1.1 Create Product Domain Structure**

- Set up staff/products/ directory structure
- Create proxy models for admin-specific behavior
- Implement base permissions and services

#### **1.2 Basic CRUD APIs**

- Product CRUD operations
- Category CRUD operations
- Brand CRUD operations
- Basic serializers with nested relationships

#### **1.3 Permission Integration**

- Integrate with the existing RBAC system
- Define product-specific permissions
- Implement role-based access control

### **Phase 2: Core Product Management (Week 2)**

#### **2.1 Product Type and Attribute Management**

- Product type CRUD with parent relationships
- Attribute CRUD operations
- Attribute value CRUD operations
- Product type attribute associations

#### **2.2 Product Variant Management**

- Product variant CRUD operations
- Variant ordering API
- Variant attribute associations
- Stock management operations

#### **2.3 Image Management**

- Product image upload and management
- Image ordering operations
- Cloudinary integration for staff uploads

### **Phase 3: Advanced Features (Week 3)**

#### **3.1 Bulk Operations**

- Bulk product creation/update
- Bulk attribute associations
- Bulk category assignments
- Bulk status updates (active/inactive)

#### **3.2 Enhanced Association Management**

- Product type attribute bulk management (replicating enhanced admin)
- Attribute value bulk management
- Search and autocomplete APIs
- Real-time association updates

#### **3.3 Review and Discount Management**

- Review moderation APIs
- Discount creation and management
- Discount application to variants
- Date-based discount validation

### **Phase 4: Advanced Analytics and Reporting (Week 4)**

#### **4.1 Product Analytics**

- Product performance metrics
- Stock level reports
- Category performance analysis
- Brand performance metrics

#### **4.2 Audit and History**

- Product change history
- Staff action logging
- Bulk operation tracking
- Data integrity reports

## **Detailed API Specifications**

### **Core Product APIs**

#### **Product Management**

```http
GET    /api/staff/products/                 # List products with filtering
POST   /api/staff/products/                 # Create product
GET    /api/staff/products/{id}/            # Get product details
PUT    /api/staff/products/{id}/            # Update product
PATCH  /api/staff/products/{id}/            # Partial update
DELETE /api/staff/products/{id}/            # Delete product
POST   /api/staff/products/bulk/            # Bulk operations
```

#### **Category Management**

```http
GET    /api/staff/products/categories/      # List categories (tree structure)
POST   /api/staff/products/categories/      # Create category
GET    /api/staff/products/categories/{id}/ # Get category details
PUT    /api/staff/products/categories/{id}/ # Update category
DELETE /api/staff/products/categories/{id}/ # Delete category
POST   /api/staff/products/categories/{id}/move/ # Move category in tree
```

#### **Product Type Management**

```http
GET    /api/staff/products/product-types/   # List product types
POST   /api/staff/products/product-types/   # Create product type
GET    /api/staff/products/product-types/{id}/ # Get product type
PUT    /api/staff/products/product-types/{id}/ # Update product type
DELETE /api/staff/products/product-types/{id}/ # Delete product type
```

### **Association Management APIs**

#### **Product Type Attributes**

```http
GET    /api/staff/products/associations/product-type-attributes/ # List associations
POST   /api/staff/products/associations/product-type-attributes/ # Create association
PUT    /api/staff/products/associations/product-type-attributes/{id}/ # Update association
DELETE /api/staff/products/associations/product-type-attributes/{id}/ # Delete association
POST   /api/staff/products/associations/product-type-attributes/bulk/ # Bulk management
```

#### **Attribute Values**

```http
GET    /api/staff/products/attribute-values/ # List with filtering by attribute
POST   /api/staff/products/attribute-values/ # Create attribute value
PUT    /api/staff/products/attribute-values/{id}/ # Update attribute value
DELETE /api/staff/products/attribute-values/{id}/ # Delete attribute value
POST   /api/staff/products/attribute-values/bulk/ # Bulk management
```

## **Permission Matrix**

### **Role-Based Access Control**

| Role                  | Products     | Categories  | Brands      | Attributes  | Reviews     | Discounts   |
| ----------------------- | -------------- | ------------- | ------------- | ------------- | ------------- | ------------- |
| **Product Manager**   | Full CRUD    | Full CRUD   | Full CRUD   | Full CRUD   | Moderate    | Full CRUD   |
| **Product Editor**    | CRUD (own)   | View/Edit   | View/Edit   | View/Edit   | View        | View        |
| **Inventory Manager** | Stock Only   | View        | View        | View        | View        | View        |
| **Content Manager**   | Edit Content | Full CRUD   | View        | View        | Moderate    | View        |
| **Admin**             | Full Access  | Full Access | Full Access | Full Access | Full Access | Full Access |

### **Specific Permissions**

```python
# Product permissions
'products.view_product'
'products.add_product'
'products.change_product'
'products.delete_product'
'products.change_product_status'
'products.bulk_update_products'

# Category permissions
'products.view_category'
'products.add_category'
'products.change_category'
'products.delete_category'
'products.move_category'

# Attribute permissions
'products.view_attribute'
'products.add_attribute'
'products.change_attribute'
'products.delete_attribute'
'products.manage_attribute_associations'

# Review permissions
'products.view_review'
'products.moderate_review'
'products.delete_review'

# Discount permissions
'products.view_discount'
'products.add_discount'
'products.change_discount'
'products.delete_discount'
'products.apply_discount'
```

## **Data Migration Strategy**

### **Proxy Models for Staff Operations**

```python
class ProductProxy(Product):
    """Proxy model for staff-specific product operations"""
    class Meta:
        proxy = True
        verbose_name = "Staff Product"
        permissions = [
            ("bulk_update_products", "Can bulk update products"),
            ("change_product_status", "Can change product status"),
        ]

class ProductAudit(models.Model):
    """Track product changes by staff"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    staff_user = models.ForeignKey(User, on_delete=models.CASCADE)
    action = models.CharField(max_length=50)
    changes = models.JSONField()
    timestamp = models.DateTimeField(auto_now_add=True)
```

### **Service Layer Architecture**

```python
class ProductService:
    """Business logic for product operations"""

    @staticmethod
    def bulk_update_products(products_data, staff_user):
        """Handle bulk product updates with audit trail"""

    @staticmethod
    def create_product_with_variants(product_data, variants_data, staff_user):
        """Create product with variants in single transaction"""

    @staticmethod
    def associate_attributes_bulk(product_type_id, attribute_data, staff_user):
        """Bulk associate attributes to product type"""
```

## **Testing Strategy**

### **API Testing Priorities**

1. **Authentication and Authorization**: Role-based access testing
2. **CRUD Operations**: Complete CRUD testing for all models
3. **Bulk Operations**: Performance and data integrity testing
4. **Association Management**: Complex relationship testing
5. **Data Validation**: Input validation and constraint testing
6. **Performance Testing**: Large dataset handling
7. **Integration Testing**: Cross-model operation testing

### **Test Coverage Requirements**

- **Unit Tests**: 90%+ coverage for services and serializers
- **Integration Tests**: All API endpoints with different roles
- **Performance Tests**: Bulk operations with 1000+ records
- **Security Tests**: Permission boundary testing

## **Migration Timeline**

### **Week 1: Foundation**

- [ ] Set up staff/products app structure
- [ ] Implement basic CRUD APIs for Product, Category, Brand
- [ ] Set up permissions and authentication
- [ ] Create proxy models and audit system
- [ ] Write comprehensive tests for basic operations

### **Week 2: Core Features**

- [ ] Implement Product Type and Attribute management
- [ ] Add Product Variant management with ordering
- [ ] Implement Image management APIs
- [ ] Add association management (ProductTypeAttribute)
- [ ] Implement search and filtering

### **Week 3: Advanced Features**

- [ ] Implement bulk operations
- [ ] Add enhanced association management (replicating admin features)
- [ ] Implement Review and Discount management
- [ ] Add autocomplete and search APIs
- [ ] Performance optimization

### **Week 4: Analytics and Polish**

- [ ] Add analytics and reporting APIs
- [ ] Implement comprehensive audit trail
- [ ] Performance testing and optimization
- [ ] Documentation and API reference
- [ ] Postman collection creation

## **Success Criteria**

### **Functional Requirements**

- [ ] All Django admin product functionalities available via API
- [ ] Role-based access control properly implemented
- [ ] Bulk operations perform efficiently (1000+ records)
- [ ] Enhanced association management features replicated
- [ ] Comprehensive audit trail for all operations

### **Performance Requirements**

- [ ] API response times < 200ms for simple operations
- [ ] Bulk operations complete within 30 seconds for 1000 records
- [ ] Concurrent user support (50+ simultaneous staff users)
- [ ] Efficient database queries (N+1 problem avoided)

### **Security Requirements**

- [ ] Proper authentication and authorization
- [ ] Input validation and sanitization
- [ ] Audit trail for all sensitive operations
- [ ] Rate limiting for bulk operations

## **Risk Mitigation**

### **Technical Risks**

- **Complex Relationships**: Extensive testing of nested serializers
- **Performance Issues**: Implement caching and query optimization
- **Data Integrity**: Use database transactions for bulk operations
- **Migration Complexity**: Gradual rollout with fallback to admin

### **Business Risks**

- **Staff Training**: Comprehensive API documentation and training
- **Downtime**: Zero-downtime deployment strategy
- **Data Loss**: Comprehensive backup and rollback procedures
- **User Adoption**: Parallel admin access during transition period

## **Next Steps**

1. **Review and Approval**: Get stakeholder approval for this plan
2. **Environment Setup**: Prepare development and testing environments
3. **Team Coordination**: Assign development tasks and timelines
4. **Documentation**: Create detailed API specifications
5. **Implementation**: Begin Phase 1 development

---

**Document Version**: 1.0
**Last Updated**: 2025-07-03
**Next Review**: Weekly during implementation
