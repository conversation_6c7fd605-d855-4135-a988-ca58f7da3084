from django.urls import path, re_path
from .views import (CustomTokenObtainPairView, CustomTokenRefreshView, CustomTokenVerifyView,
                    LogoutView, InitiateRegistrationView, VerifyCodeView, SetPasswordView,
                    CustomLoginView, CurrentUserView, PasswordResetRequestView,
                    PasswordResetConfirmView, ChangePasswordView, ChangePrimaryAuthMethodView,
                    GoogleLoginView, FacebookLoginView, UpdateContactInfo, VerifyUpdateContactInfo)

app_name = 'core'

urlpatterns = [
    # Authentication endpoints - unified for both regular and staff users
    path('login/', CustomLoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('me/', CurrentUserView.as_view(), name='current-user'),

    # JWT token management
    path('token/create/', CustomTokenObtainPairView.as_view(), name='token-create'),
    path('token/refresh/', CustomTokenRefreshView.as_view(), name='token-refresh'),
    path('token/verify/', CustomTokenVerifyView.as_view(), name='token-verify'),

    # User registration flow
    path('register/initiate/', InitiateRegistrationView.as_view(), name='initiate-registration'),
    path('register/verify/', VerifyCodeView.as_view(), name='verify-code'),
    path('register/set-password/', SetPasswordView.as_view(), name='set-password'),

    # Password management
    path('password/reset/request/', PasswordResetRequestView.as_view(), name='password-reset-request'),
    path('password/reset/confirm/', PasswordResetConfirmView.as_view(), name='password-reset-confirm'),
    path('password/change/', ChangePasswordView.as_view(), name='change-password'),

    # Profile management
    path('profile/change-auth-method/', ChangePrimaryAuthMethodView.as_view(), name='change-primary-auth-method'),
    path('profile/contact/update/', UpdateContactInfo.as_view(), name='update-contact-info'),
    path('profile/contact/verify/', VerifyUpdateContactInfo.as_view(), name='verify-contact-update'),

    # Social authentication endpoints
    path('social/google/', GoogleLoginView.as_view(), name='google-login'),
    path('social/facebook/', FacebookLoginView.as_view(), name='facebook-login'),
    path('social/facebook/complete/', FacebookLoginView.as_view(), name='facebook-complete'),
]
