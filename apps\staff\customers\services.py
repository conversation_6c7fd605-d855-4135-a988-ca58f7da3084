from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal
from .models import CustomerProxy, AddressProxy
from apps.customers.models import Customer
from apps.order.models import Order


class CustomerAnalyticsService:
    """Service for customer analytics and reporting"""
    
    @staticmethod
    def get_customer_analytics(days=30):
        """Get comprehensive customer analytics"""
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # Base customer queryset
        customers = CustomerProxy.objects.select_related('user').prefetch_related('order_set')
        
        # Basic counts
        total_customers = customers.count()
        active_customers = customers.filter(user__is_active=True).count()
        new_customers_this_month = customers.filter(
            user__date_joined__gte=cutoff_date
        ).count()
        
        # Customer segments
        segments = {'VIP': 0, 'LOYAL': 0, 'REPEAT': 0, 'NEW': 0, 'PROSPECT': 0}
        for customer in customers:
            segment = customer.get_customer_segment()
            segments[segment] = segments.get(segment, 0) + 1
        
        # Order analytics
        orders = Order.objects.filter(payment_status='Paid')
        total_revenue = orders.aggregate(total=Sum('total'))['total'] or Decimal('0.00')
        average_order_value = orders.aggregate(avg=Avg('total'))['avg'] or Decimal('0.00')
        
        # Customer lifetime value
        customer_lifetime_value = total_revenue / total_customers if total_customers > 0 else Decimal('0.00')
        
        # Repeat customer rate
        repeat_customers = customers.annotate(
            order_count=Count('order')
        ).filter(order_count__gt=1).count()
        repeat_customer_rate = (repeat_customers / total_customers * 100) if total_customers > 0 else 0
        
        # Top customers
        top_customers = CustomerAnalyticsService._get_top_customers()
        
        # Geographic distribution
        geographic_distribution = CustomerAnalyticsService._get_geographic_distribution()
        
        # Customer acquisition trends
        acquisition_trends = CustomerAnalyticsService._get_acquisition_trends(days)
        
        return {
            'total_customers': total_customers,
            'active_customers': active_customers,
            'new_customers_this_month': new_customers_this_month,
            'customer_segments': segments,
            'average_order_value': round(average_order_value, 2),
            'customer_lifetime_value': round(customer_lifetime_value, 2),
            'repeat_customer_rate': round(repeat_customer_rate, 2),
            'top_customers': top_customers,
            'geographic_distribution': geographic_distribution,
            'customer_acquisition_trends': acquisition_trends
        }
    
    @staticmethod
    def _get_top_customers(limit=10):
        """Get top customers by total spending"""
        customers = CustomerProxy.objects.annotate(
            total_spent=Sum('order__total', filter=Q(order__payment_status='Paid')),
            orders_count=Count('order', filter=Q(order__payment_status='Paid'))
        ).filter(
            total_spent__isnull=False
        ).order_by('-total_spent')[:limit]
        
        return [
            {
                'id': customer.id,
                'name': f"{customer.first_name} {customer.last_name}".strip(),
                'email': customer.user.email if customer.user else None,
                'total_spent': float(customer.total_spent or 0),
                'orders_count': customer.orders_count,
                'segment': customer.get_customer_segment()
            }
            for customer in customers
        ]
    
    @staticmethod
    def _get_geographic_distribution():
        """Get customer distribution by city"""
        distribution = AddressProxy.objects.values(
            'city_or_village'
        ).annotate(
            customer_count=Count('customer', distinct=True)
        ).order_by('-customer_count')[:10]
        
        return {
            item['city_or_village']: item['customer_count']
            for item in distribution
        }
    
    @staticmethod
    def _get_acquisition_trends(days=30):
        """Get customer acquisition trends"""
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # Daily acquisition for the period
        daily_acquisitions = {}
        customers = CustomerProxy.objects.filter(
            user__date_joined__gte=cutoff_date
        ).select_related('user')
        
        for customer in customers:
            date_key = customer.user.date_joined.date().isoformat()
            daily_acquisitions[date_key] = daily_acquisitions.get(date_key, 0) + 1
        
        # Acquisition sources (placeholder - would integrate with marketing data)
        acquisition_sources = {
            'organic': 45,
            'social_media': 25,
            'email_marketing': 15,
            'paid_ads': 10,
            'referral': 5
        }
        
        return {
            'daily_acquisitions': daily_acquisitions,
            'acquisition_sources': acquisition_sources,
            'growth_rate': CustomerAnalyticsService._calculate_growth_rate(days)
        }
    
    @staticmethod
    def _calculate_growth_rate(days=30):
        """Calculate customer growth rate"""
        cutoff_date = timezone.now() - timedelta(days=days)
        previous_cutoff = cutoff_date - timedelta(days=days)
        
        current_period = CustomerProxy.objects.filter(
            user__date_joined__gte=cutoff_date
        ).count()
        
        previous_period = CustomerProxy.objects.filter(
            user__date_joined__gte=previous_cutoff,
            user__date_joined__lt=cutoff_date
        ).count()
        
        if previous_period > 0:
            growth_rate = ((current_period - previous_period) / previous_period) * 100
        else:
            growth_rate = 100 if current_period > 0 else 0
        
        return round(growth_rate, 2)


class CustomerManagementService:
    """Service for customer management operations"""
    
    @staticmethod
    def get_customer_activity(customer_id, days=30):
        """Get detailed customer activity"""
        try:
            customer = CustomerProxy.objects.get(id=customer_id)
        except CustomerProxy.DoesNotExist:
            return None
        
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # Recent orders
        recent_orders = customer.order_set.filter(
            placed_at__gte=cutoff_date
        ).order_by('-placed_at')[:10]
        
        # Recent wishlist items
        recent_wishlist = customer.wishlists.filter(
            added_at__gte=cutoff_date
        ).select_related('product').order_by('-added_at')[:10]
        
        # Support history (placeholder)
        support_history = customer.get_support_history()
        
        # Browsing patterns (placeholder - would integrate with analytics)
        browsing_patterns = {
            'most_viewed_categories': [],
            'search_queries': [],
            'session_duration_avg': 0,
            'pages_per_session': 0
        }
        
        return {
            'recent_orders': [
                {
                    'id': order.id,
                    'total': float(order.total),
                    'status': order.payment_status,
                    'placed_at': order.placed_at,
                    'items_count': order.ordered_items.count()
                }
                for order in recent_orders
            ],
            'recent_wishlist_items': [
                {
                    'id': item.id,
                    'product_title': item.product.title,
                    'added_at': item.added_at
                }
                for item in recent_wishlist
            ],
            'last_login': customer.user.last_login if customer.user else None,
            'support_history': support_history,
            'browsing_patterns': browsing_patterns
        }
    
    @staticmethod
    def bulk_update_customers(customer_ids, operation, performed_by, **kwargs):
        """Bulk update customers"""
        updated_count = 0
        errors = []
        
        for customer_id in customer_ids:
            try:
                customer = CustomerProxy.objects.get(id=customer_id)
                
                if operation == 'activate':
                    if customer.user:
                        customer.user.is_active = True
                        customer.user.save()
                        updated_count += 1
                
                elif operation == 'deactivate':
                    if customer.user:
                        customer.user.is_active = False
                        customer.user.save()
                        updated_count += 1
                
                elif operation == 'segment':
                    # This would update a customer segment field if it existed
                    # For now, just count as updated
                    updated_count += 1
                
            except CustomerProxy.DoesNotExist:
                errors.append(f"Customer {customer_id} not found")
            except Exception as e:
                errors.append(f"Error updating customer {customer_id}: {str(e)}")
        
        return {
            'updated_count': updated_count,
            'errors': errors,
            'total_requested': len(customer_ids)
        }
    
    @staticmethod
    def export_customer_data(customer_ids):
        """Export customer data for analysis"""
        customers = CustomerProxy.objects.filter(
            id__in=customer_ids
        ).select_related('user').prefetch_related(
            'address', 'order_set', 'wishlists'
        )
        
        export_data = []
        for customer in customers:
            customer_data = {
                'customer_id': customer.id,
                'first_name': customer.first_name,
                'last_name': customer.last_name,
                'email': customer.user.email if customer.user else None,
                'phone_number': str(customer.user.phone_number) if customer.user and customer.user.phone_number else None,
                'birth_date': customer.birth_date.isoformat() if customer.birth_date else None,
                'date_joined': customer.user.date_joined.isoformat() if customer.user else None,
                'last_login': customer.user.last_login.isoformat() if customer.user and customer.user.last_login else None,
                'is_active': customer.user.is_active if customer.user else False,
                'orders_count': customer.get_orders_count(),
                'total_spent': float(customer.get_total_spent()),
                'wishlist_count': customer.get_wishlist_count(),
                'addresses_count': customer.address.count(),
                'customer_segment': customer.get_customer_segment(),
                'addresses': [
                    {
                        'full_name': addr.full_name,
                        'street_name': addr.street_name,
                        'city': addr.city_or_village,
                        'postal_code': addr.postal_code,
                    }
                    for addr in customer.address.all()
                ]
            }
            export_data.append(customer_data)
        
        return export_data
