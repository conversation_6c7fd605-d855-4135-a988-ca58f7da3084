# Stage 1
FROM python:3-alpine AS builder

WORKDIR /app

RUN python3 -m venv venv
ENV VIRTUAL_ENV=/app/venv
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

COPY requirements.txt .
RUN pip install -r requirements.txt

# Stage 2
FROM python:3-alpine AS runner

WORKDIR /app

COPY --from=builder /app/venv venv
COPY . /app/
COPY entrypoint.sh /app/

ENV VIRTUAL_ENV=/app/venv
ENV PATH="$VIRTUAL_ENV/bin:$PATH"
ENV PORT=8000
ENV DJANGO_SETTINGS_MODULE=pc_hardware.settings.production

# Create a non-root user
RUN adduser -D appuser

# Create static directory and set ownership
RUN mkdir -p /app/static && chown -R appuser:appuser /app/static

# Make the entrypoint script executable
RUN chmod +x /app/entrypoint.sh

# Collect static files
# This gives an error during Docker build process.
# SECRET_KEY environment variable is missing when trying to run the collectstatic command.
# RUN python manage.py collectstatic --noinput

# Switch to non-root user
USER appuser

# Expose the port the application will run on
EXPOSE ${PORT}

# Use entrypoint to run the script
ENTRYPOINT ["/app/entrypoint.sh"]

# other ways to run CMD:
# CMD ["gunicorn", "--bind", ":8000", "--workers", "2", "pc_hardware.wsgi:application"]
# CMD gunicorn pc_hardware.wsgi:application
# CMD gunicorn --bind :${PORT} --workers 2 pc_hardware.wsgi:application