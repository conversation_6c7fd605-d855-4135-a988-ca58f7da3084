# Order Management System

## Existing Implementation

### Overview

The current order management system includes the following components:

- **Models**: `Order`, `OrderItem`, `BraintreeTransaction`.
- **Serializers**: `OrderSerializer`, `CreateOrderSerializer`, `UpdateOrderDeliveryStatusSerializer`.
- **Views**: `OrderViewSet`.
- **Admin**: `OrderAdmin` with inline `OrderItem` management.

### Strengths

- Basic order creation and tracking are implemented.
- Integration with payment gateways (e.g., Stripe, Braintree).
- Signals are used for decoupled actions (e.g., stock updates, notifications).

### Weaknesses

- No customer-facing order tracking system.
- Limited admin functionality (e.g., no bulk updates, limited filtering).
- No integration with shipping providers.
- Notifications for order status updates are missing.

## Step-by-Step Guide to Improve the Order Management System

### 1. Add Order Tracking for Customers

#### Steps

1. Extend the `Order` model to include a `tracking_number` field.
2. Create a new API endpoint in `OrderViewSet` to fetch order tracking details.
3. Update the frontend to display tracking information to customers.

#### Code Changes

- **File**: `apps/order/models.py`

  ```python
  tracking_number = models.CharField(max_length=50, blank=True, null=True)
  ```

- **File**: `apps/order/views.py`

  ```python
  @action(detail=True, methods=['get'])
  def track_order(self, request, pk=None):
      order = self.get_object()
      return Response({"tracking_number": order.tracking_number})
  ```

### 2. Enhance Admin Dashboard

#### Steps

1. Add filters for `payment_status` and `order_status` in `OrderAdmin`.
2. Implement bulk actions for updating order statuses.

#### Code Changes

- **File**: `apps/order/admin.py`

  ```python
  list_filter = ['payment_status', 'order_status', 'placed_at']
  
  def mark_as_dispatched(self, request, queryset):
      queryset.update(order_status='Dispatched')
  mark_as_dispatched.short_description = "Mark selected orders as Dispatched"
  
  actions = [mark_as_dispatched]
  ```

---

### 3. Integrate Shipping Providers

#### Steps

1. Use a third-party library (e.g., `django-easypost`) for shipping label generation.
2. Add a `shipping_label` field to the `Order` model.
3. Create a Celery task to generate shipping labels asynchronously.

#### Code Changes

- **File**: `apps/order/models.py`

  ```python
  shipping_label = models.URLField(blank=True, null=True)
  ```

- **File**: `apps/order/tasks.py`

  ```python
  from easypost import Shipment
  
  def generate_shipping_label(order_id):
      order = Order.objects.get(pk=order_id)
      shipment = Shipment.create(...)
      order.shipping_label = shipment.postage_label.label_url
      order.save()
  ```

---

### 4. Add Notifications

#### Steps

1. Use Django signals to trigger notifications on order status updates.
2. Integrate with an email/SMS service (e.g., Twilio, SendGrid).

#### Code Changes

- **File**: `apps/order/signals.py`

  ```python
  from django.db.models.signals import post_save
  from django.dispatch import receiver
  from .models import Order
  
  @receiver(post_save, sender=Order)
  def send_order_update_notification(sender, instance, **kwargs):
      if kwargs['created']:
          send_email(instance.customer.email, "Order Created", "Your order has been created.")
      else:
          send_email(instance.customer.email, "Order Updated", f"Your order status is now {instance.order_status}.")
  ```

---

## Conclusion

These improvements will make the order management system more robust, user-friendly, and scalable. Each step can be
implemented independently to allow for incremental progress.
