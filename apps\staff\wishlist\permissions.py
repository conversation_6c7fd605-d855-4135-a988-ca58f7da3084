from rest_framework.permissions import BasePermission
from apps.staff.authorization.permissions import CanAccessStaffAPI
from apps.staff.common.constants import STAFF_GROUPS


class CanManageWishlists(BasePermission):
    """
    Permission class for wishlist management operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        # Check basic staff API access
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check specific wishlist permissions based on action
        if hasattr(view, 'action'):
            action = view.action
            
            if action in ['list', 'retrieve']:
                return request.user.has_perm('wishlist.view_all_wishlists')
            elif action in ['destroy', 'bulk_operations']:
                return request.user.has_perm('wishlist.bulk_wishlist_operations')
            elif action == 'analytics':
                return request.user.has_perm('wishlist.wishlist_analytics')
            elif action in ['behavior_analysis', 'customer_insights']:
                return request.user.has_perm('wishlist.customer_behavior_analysis')
            elif action in ['marketing_insights', 'conversion_opportunities']:
                return request.user.has_perm('wishlist.marketing_insights')
        
        return request.user.has_perm('wishlist.view_all_wishlists')


class CanViewWishlistAnalytics(BasePermission):
    """
    Permission class for wishlist analytics operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check if user has analytics permissions
        user_groups = set(request.user.groups.values_list('name', flat=True))
        allowed_groups = {
            STAFF_GROUPS['MARKETING_MANAGER'],
            STAFF_GROUPS['CUSTOMER_SERVICE_MANAGER'],
            STAFF_GROUPS['PRODUCT_MANAGER'],
            STAFF_GROUPS['SYSTEM_ADMINISTRATOR']
        }
        
        return (
            request.user.is_superuser or
            bool(user_groups.intersection(allowed_groups)) or
            request.user.has_perm('wishlist.wishlist_analytics')
        )


class CanAccessCustomerBehavior(BasePermission):
    """
    Permission class for customer behavior analysis
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check if user has behavior analysis permissions
        user_groups = set(request.user.groups.values_list('name', flat=True))
        allowed_groups = {
            STAFF_GROUPS['MARKETING_MANAGER'],
            STAFF_GROUPS['CUSTOMER_SERVICE_MANAGER'],
            STAFF_GROUPS['PRODUCT_MANAGER']
        }
        
        return (
            request.user.is_superuser or
            bool(user_groups.intersection(allowed_groups)) or
            request.user.has_perm('wishlist.customer_behavior_analysis')
        )


class CanAccessMarketingInsights(BasePermission):
    """
    Permission class for marketing insights access
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check if user has marketing insights permissions
        user_groups = set(request.user.groups.values_list('name', flat=True))
        allowed_groups = {
            STAFF_GROUPS['MARKETING_MANAGER'],
            STAFF_GROUPS['SYSTEM_ADMINISTRATOR']
        }
        
        return (
            request.user.is_superuser or
            bool(user_groups.intersection(allowed_groups)) or
            request.user.has_perm('wishlist.marketing_insights')
        )


class IsMarketingStaff(BasePermission):
    """
    Permission class for marketing staff
    Limited to marketing-related operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        return (
            request.user.is_superuser or
            request.user.groups.filter(name__in=[
                'Marketing Manager (MM)',
                'Marketing Team Member (MTM)'
            ]).exists()
        )
    
    def has_object_permission(self, request, view, obj):
        # Marketing staff can access wishlist data for insights
        if hasattr(view, 'action') and view.action in ['retrieve', 'analytics', 'marketing_insights']:
            return True
        return self.has_permission(request, view)
