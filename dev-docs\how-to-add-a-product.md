# How to Add a Product to the Store

Follow these steps to add a product to the store. Make sure you complete each step in sequence for proper setup.

### Step 1: Add a Product Category

If the product's **Category** doesn't already exist, create it.
Categories have a tree-like structure; each category might have parents,
sub-categories, and sub-sub-categories etc. <br>
_(e.g.: Hard Drives > Internal/External > SSDs/HDDs > SATA/M.2/PCIe)._

### Step 2: Add a Product Type

If the product's **Product Type** is not already listed, add it to the store. <br>
Unlike Category (which has a tree-like structure), Product Type is a flat list.
When adding a Product Type, make it singular and use dashes to separate words.
Product Types are more like highest-level of product categories.
_(e.g.: hard-drive, monitor, etc.)_

### Step 3: Add a Brand

If the product's **Brand** hasn't been added yet, add the brand.
When adding a brand, make it capitalized or as you wish to display that on frontend. <br>
_(e.g.: Samsung, HP, Dell, etc.)_

### Step 4: Associate Product Type with Brand

Next, associate the **Product Type** with the relevant **Brand** in the **Brand Product Types** table.
It is possible to associate multiple Brand with a single Product Type.

### Step 5: Add Product Attributes

Add product's **Attributes**. If these attributes aren't already available, add them.
To avoid confusion, focus on one **Product Type** at a time. <br>
Add the text-style as you want it to appear in front-end app.
_Monitor: (e.g., Display Size, Resolution, etc.)
Hard Drive: (e.g., Storage Capacity, Hard Drive RPM, etc.)_

### Step 6: Associate Product Type with Attributes

Now, associate **Product Type** with the **Attributes** in the **Product Type Attributes** table.

### Step 7: Add Attribute Values

For each **Attribute** (e.g., color), add the corresponding **Attribute Values** (e.g., red, blue).
If an attribute value already exists, do not add it again.
Incorrect: (e.g., color: red, color: Red)

### Step 8: Add the Product

Now, you can add the product with the relevant details (name, description, etc.)
in the **Product** table.

### Step 9: Edit Product Variants

If the product has **variants** (e.g., different sizes, colors), add one or more variants.

Product images should be added here.

### Step 10: Link Product Variants with Attribute Values

Finally, **associate each product variant** with its respective **attribute values** (e.g., size: medium, color: blue).

---

You’re all set! After completing these steps, your product should be fully added to the store with all necessary
details.
