import requests
from django.conf import settings
from datetime import datetime, timedelta


class PayPalClient:
    """Handles PayPal API authentication and operations"""

    def __init__(self):
        # Initialize PayPal credentials from Django settings
        self.client_id = settings.PAYPAL_CLIENT_ID
        self.client_secret = settings.PAYPAL_CLIENT_SECRET
        self.base_url = settings.PAYPAL_BASE_URL
        # Cache for access token and its expiry
        self._access_token = None
        self._token_expiry = None

    def _get_access_token(self):
        """
        Retrieves or refreshes PayPal OAuth access token
        Returns cached token if valid, otherwise requests new one
        """
        if self._access_token and self._token_expiry and datetime.now() < self._token_expiry:
            return self._access_token

        auth_url = f"{self.base_url}/v1/oauth2/token"
        headers = {
            "Accept": "application/json",
            "Accept-Language": "en_US"
        }
        data = {"grant_type": "client_credentials"}

        # Request new access token using client credentials
        response = requests.post(
            auth_url,
            headers=headers,
            data=data,
            auth=(self.client_id, self.client_secret)
        )
        response.raise_for_status()

        result = response.json()
        # Cache the new token and calculate expiry time
        self._access_token = result["access_token"]
        self._token_expiry = datetime.now() + timedelta(seconds=result["expires_in"])

        return self._access_token

    def create_order(self, order_data):
        """
        Creates a PayPal order with the provided order details
        Returns the PayPal order response with ID and status
        """
        url = f"{self.base_url}/v2/checkout/orders"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self._get_access_token()}"
        }
        
        response = requests.post(url, headers=headers, json=order_data)
        response.raise_for_status()
        return response.json()

    def capture_order(self, order_id):
        """
        Captures payment for an approved PayPal order
        Used after buyer approves payment on PayPal side
        """
        url = f"{self.base_url}/v2/checkout/orders/{order_id}/capture"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self._get_access_token()}"
        }

        response = requests.post(url, headers=headers)
        response.raise_for_status()
        return response.json()

    def verify_webhook_signature(self, webhook_id, webhook_event):
        """
        Verifies authenticity of PayPal webhook notifications
        Ensures webhook events are legitimate and from PayPal
        """
        url = f"{self.base_url}/v1/notifications/verify-webhook-signature"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self._get_access_token()}"
        }

        response = requests.post(url, headers=headers, json=webhook_event)
        response.raise_for_status()
        return response.json()
