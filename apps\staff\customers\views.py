from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Count, Sum, Q, Prefetch
from .models import CustomerProxy, AddressProxy
from .serializers import (
    CustomerStaffSerializer, CustomerSummarySerializer, AddressStaffSerializer,
    CustomerAnalyticsSerializer, CustomerActivitySerializer, BulkCustomerOperationSerializer,
    CustomerUpdateSerializer
)
from .permissions import (
    CanManageCustomers, CanViewCustomerAnalytics, CanAccessCustomerSupport,
    CanManageAddresses
)
from .filters import CustomerStaffFilter, AddressStaffFilter
from .services import CustomerAnalyticsService, CustomerManagementService
from apps.staff.common.utils import paginate_queryset


class CustomerStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff customer management
    Provides full CRUD operations with role-based permissions
    """
    queryset = CustomerProxy.objects.all().select_related('user').prefetch_related('address')
    permission_classes = [CanManageCustomers]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = CustomerStaffFilter
    search_fields = ['first_name', 'last_name', 'user__email', 'user__phone_number']
    ordering_fields = ['first_name', 'last_name', 'user__date_joined', 'user__last_login']
    ordering = ['-user__date_joined']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return CustomerSummarySerializer
        elif self.action in ['update', 'partial_update']:
            return CustomerUpdateSerializer
        return CustomerStaffSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Optimize queryset with annotations for list view
        if self.action == 'list':
            queryset = queryset.annotate(
                _orders_count=Count('order', filter=Q(order__payment_status='Paid')),
                _total_spent=Sum('order__total', filter=Q(order__payment_status='Paid')),
                _wishlist_count=Count('wishlists')
            )
        
        return queryset
    
    @action(detail=False, methods=['get'], permission_classes=[CanViewCustomerAnalytics])
    def analytics(self, request):
        """Get customer analytics data"""
        days = int(request.query_params.get('days', 30))
        analytics_data = CustomerAnalyticsService.get_customer_analytics(days)
        
        serializer = CustomerAnalyticsSerializer(analytics_data)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'], permission_classes=[CanAccessCustomerSupport])
    def activity(self, request, pk=None):
        """Get detailed customer activity"""
        days = int(request.query_params.get('days', 30))
        activity_data = CustomerManagementService.get_customer_activity(pk, days)
        
        if not activity_data:
            return Response(
                {'error': 'Customer not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        serializer = CustomerActivitySerializer(activity_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def bulk_operations(self, request):
        """Perform bulk operations on customers"""
        serializer = BulkCustomerOperationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        customer_ids = serializer.validated_data['customer_ids']
        operation = serializer.validated_data['operation']
        reason = serializer.validated_data.get('reason', '')
        
        if operation == 'export':
            # Export customer data
            export_data = CustomerManagementService.export_customer_data(customer_ids)
            return Response({
                'success': True,
                'data': export_data,
                'count': len(export_data)
            })
        else:
            # Other bulk operations
            result = CustomerManagementService.bulk_update_customers(
                customer_ids=customer_ids,
                operation=operation,
                performed_by=request.user,
                reason=reason,
                **serializer.validated_data
            )
            
            return Response({
                'success': True,
                'message': f'Bulk {operation} completed',
                'updated_count': result['updated_count'],
                'errors': result['errors'],
                'total_requested': result['total_requested']
            })
    
    @action(detail=False, methods=['get'])
    def segments(self, request):
        """Get customer segment distribution"""
        segments = {'VIP': 0, 'LOYAL': 0, 'REPEAT': 0, 'NEW': 0, 'PROSPECT': 0}
        
        # This would be optimized in a real implementation
        customers = self.get_queryset()
        for customer in customers:
            segment = customer.get_customer_segment()
            segments[segment] = segments.get(segment, 0) + 1
        
        return Response(segments)
    
    @action(detail=True, methods=['get'], permission_classes=[CanAccessCustomerSupport])
    def support_history(self, request, pk=None):
        """Get customer support history"""
        customer = self.get_object()
        support_history = customer.get_support_history()
        
        return Response(support_history)


class AddressStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff address management
    """
    queryset = AddressProxy.objects.all().select_related('customer__user')
    serializer_class = AddressStaffSerializer
    permission_classes = [CanManageAddresses]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = AddressStaffFilter
    search_fields = ['full_name', 'street_name', 'city_or_village', 'postal_code']
    ordering_fields = ['full_name', 'city_or_village', 'postal_code']
    ordering = ['customer__first_name', 'customer__last_name']
    
    @action(detail=False, methods=['get'])
    def geographic_distribution(self, request):
        """Get geographic distribution of addresses"""
        distribution = self.get_queryset().values(
            'city_or_village'
        ).annotate(
            count=Count('id'),
            customers=Count('customer', distinct=True)
        ).order_by('-count')[:20]
        
        return Response(list(distribution))
    
    @action(detail=True, methods=['get'])
    def usage_stats(self, request, pk=None):
        """Get usage statistics for an address"""
        address = self.get_object()
        
        stats = {
            'orders_count': address.get_orders_count(),
            'is_primary': address.is_primary_address(),
            'customer_info': {
                'id': address.customer.id,
                'name': f"{address.customer.first_name} {address.customer.last_name}".strip(),
                'email': address.customer.user.email if address.customer.user else None,
            }
        }
        
        return Response(stats)
