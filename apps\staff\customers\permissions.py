from rest_framework.permissions import BasePermission
from apps.staff.authorization.permissions import CanAccessStaffAPI
from apps.staff.common.constants import STAFF_GROUPS


class CanManageCustomers(BasePermission):
    """
    Permission class for customer management operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        # Check basic staff API access
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check specific customer permissions based on action
        if hasattr(view, 'action'):
            action = view.action
            
            if action in ['list', 'retrieve']:
                return request.user.has_perm('customers.view_all_customers')
            elif action in ['update', 'partial_update']:
                return request.user.has_perm('customers.manage_customer_data')
            elif action in ['bulk_operations', 'export']:
                return request.user.has_perm('customers.bulk_customer_operations')
            elif action == 'analytics':
                return request.user.has_perm('customers.customer_analytics')
            elif action in ['support_history', 'recent_activity']:
                return request.user.has_perm('customers.customer_support_access')
        
        return request.user.has_perm('customers.view_all_customers')


class CanViewCustomerAnalytics(BasePermission):
    """
    Permission class for customer analytics operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check if user has analytics permissions
        user_groups = set(request.user.groups.values_list('name', flat=True))
        allowed_groups = {
            STAFF_GROUPS['CUSTOMER_SERVICE_MANAGER'],
            STAFF_GROUPS['CUSTOMER_SERVICE_REPRESENTATIVE'],
            STAFF_GROUPS['MARKETING_MANAGER'],
            STAFF_GROUPS['SYSTEM_ADMINISTRATOR']
        }
        
        return (
            request.user.is_superuser or
            bool(user_groups.intersection(allowed_groups)) or
            request.user.has_perm('customers.customer_analytics')
        )


class CanAccessCustomerSupport(BasePermission):
    """
    Permission class for customer support operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check if user has customer support permissions
        user_groups = set(request.user.groups.values_list('name', flat=True))
        allowed_groups = {
            STAFF_GROUPS['CUSTOMER_SERVICE_MANAGER'],
            STAFF_GROUPS['CUSTOMER_SERVICE_REPRESENTATIVE'],
            STAFF_GROUPS['ORDER_MANAGER'],
            STAFF_GROUPS['ORDER_TEAM_MEMBER']
        }
        
        return (
            request.user.is_superuser or
            bool(user_groups.intersection(allowed_groups)) or
            request.user.has_perm('customers.customer_support_access')
        )


class CanManageAddresses(BasePermission):
    """
    Permission class for address management operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        if not CanAccessStaffAPI().has_permission(request, view):
            return False
        
        # Check specific address permissions based on action
        if hasattr(view, 'action'):
            action = view.action
            
            if action in ['list', 'retrieve']:
                return request.user.has_perm('customers.view_all_addresses')
            elif action in ['create', 'update', 'partial_update', 'destroy']:
                return request.user.has_perm('customers.manage_customer_addresses')
        
        return request.user.has_perm('customers.view_all_addresses')


class IsCustomerServiceStaff(BasePermission):
    """
    Permission class for customer service staff
    Limited to customer service operations
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated and request.user.is_staff):
            return False
        
        return (
            request.user.is_superuser or
            request.user.groups.filter(name__in=[
                'Customer Service Manager (CSM)',
                'Customer Service Representative (CSR)'
            ]).exists()
        )
    
    def has_object_permission(self, request, view, obj):
        # Customer service staff can access customer data for support
        if hasattr(view, 'action') and view.action in ['retrieve', 'update', 'partial_update']:
            return True
        return self.has_permission(request, view)
