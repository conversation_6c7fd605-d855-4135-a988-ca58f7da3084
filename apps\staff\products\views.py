from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Q, Count, Sum, Avg, Max
from django.db import transaction
from django.shortcuts import get_object_or_404

from .models import (
    ProductProxy, CategoryProxy, BrandProxy, ProductTypeProxy, AttributeProxy, AttributeValueProxy,
    ProductVariantProxy, ProductImageProxy, ReviewProxy, DiscountProxy, ProductTypeAttributeProxy,
    ProductAttributeValueProxy, ProductVariantAttributeValueProxy, BrandProductTypeProxy,
    ProductAudit, BulkProductOperation
)
from .serializers import (
    ProductStaffSerializer, ProductListStaffSerializer, CategoryStaffSerializer,
    BrandStaffSerializer, ProductTypeStaffSerializer, AttributeStaffSerializer,
    AttributeValueStaffSerializer, ProductVariantStaffSerializer,
    ProductImageStaffSerializer, ReviewStaffSerializer, DiscountStaffSerializer,
    BulkProductSerializer, BulkAttributeAssociationSerializer,
    ProductAuditSerializer, BulkOperationSerializer,
    ProductCreateWithVariantsSerializer, CategoryMoveSerializer,
    ProductAnalyticsSerializer, ProductTypeAttributeStaffSerializer,
    BrandProductTypeStaffSerializer, ProductAttributeValueStaffSerializer,
    ProductVariantAttributeValueStaffSerializer, ProductVariantAttributeValueDetailSerializer,
    BulkBrandProductTypeSerializer, BulkProductTypeBrandSerializer, BulkProductAttributeValueSerializer,
    BulkVariantAttributeValueSerializer, BulkOrderUpdateSerializer,
    ProductTypeAttributeValuesResponseSerializer
)
from .permissions import (
    CanManageProducts, CanManageCategories, CanManageBrands,
    CanManageProductTypes, CanManageAttributes, CanManageReviews,
    CanManageDiscounts, IsProductManager, IsInventoryManager
)
from .filters import (
    ProductStaffFilter, CategoryStaffFilter, BrandStaffFilter, ProductTypeStaffFilter,
    ProductVariantStaffFilter, ProductImageStaffFilter, AttributeStaffFilter, AttributeValueStaffFilter,
    ReviewStaffFilter, DiscountStaffFilter, BrandProductTypeStaffFilter,
    ProductTypeAttributeStaffFilter, ProductAttributeValueStaffFilter,
    ProductVariantAttributeValueStaffFilter
)
from .services import ProductService, CategoryService, BrandProductTypeService, ProductTypeBrandService, AuditService
from apps.staff.common.utils import paginate_queryset


class ProductStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff product management
    Provides full CRUD operations with role-based permissions
    Uses unified authentication from core app - no explicit authentication_classes needed
    """
    queryset = ProductProxy.objects.all().select_related(
        'brand', 'category', 'product_type'
    ).prefetch_related(
        'product_variant', 'reviews', 'attribute_value'
    )
    permission_classes = [CanManageProducts]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = ProductStaffFilter
    search_fields = ['title', 'description', 'brand__title', 'category__title']
    ordering_fields = ['title', 'created_at', 'updated_at', 'average_rating']
    ordering = ['-updated_at']

    def get_serializer_class(self):
        if self.action == 'list':
            return ProductListStaffSerializer
        elif self.action == 'create_with_variants':
            return ProductCreateWithVariantsSerializer
        return ProductStaffSerializer

    def get_queryset(self):
        queryset = super().get_queryset()

        # Add annotations for better performance
        queryset = queryset.annotate(
            variants_count=Count('product_variant'),
            total_stock=Sum('product_variant__stock_qty'),
            reviews_count=Count('reviews')
        )

        return queryset

    def perform_create(self, serializer):
        product = serializer.save()

        # Log the creation
        # AuditService.log_product_action(
        #     product=product,
        #     staff_user=self.request.user,
        #     action='CREATE',
        #     changes=serializer.validated_data,
        #     request=self.request
        # )

    def perform_update(self, serializer):
        # Store original values for audit
        original_values = {}
        for field in serializer.validated_data.keys():
            original_values[field] = getattr(serializer.instance, field)

        product = serializer.save()

        # Log the update
        # AuditService.log_product_action(
        #     product=product,
        #     staff_user=self.request.user,
        #     action='UPDATE',
        #     changes={
        #         'original': original_values,
        #         'updated': serializer.validated_data
        #     },
        #     request=self.request
        # )

    def perform_destroy(self, instance):
        # Log the deletion before actually deleting
        AuditService.log_product_action(
            product=instance,
            staff_user=self.request.user,
            action='DELETE',
            changes={'deleted_product_data': {
                'title': instance.title,
                'id': instance.id
            }},
            request=self.request
        )
        instance.delete()

    @action(detail=False, methods=['post'], permission_classes=[CanManageProducts])
    def bulk_operations(self, request):
        """
        Handle bulk operations on products
        """
        serializer = BulkProductSerializer(data=request.data)
        if serializer.is_valid():
            operation_type = serializer.validated_data['operation_type']
            product_ids = serializer.validated_data['product_ids']
            data = serializer.validated_data.get('data', {})

            try:
                if operation_type == 'update':
                    result = ProductService.bulk_update_products(
                        products_data=[{**data, 'id': pid} for pid in product_ids],
                        staff_user=request.user,
                        request=request
                    )
                elif operation_type == 'activate':
                    ProductProxy.objects.filter(id__in=product_ids).update(is_active=True)
                    result = {'updated_count': len(product_ids)}
                elif operation_type == 'deactivate':
                    ProductProxy.objects.filter(id__in=product_ids).update(is_active=False)
                    result = {'updated_count': len(product_ids)}
                elif operation_type == 'delete':
                    ProductProxy.objects.filter(id__in=product_ids).delete()
                    result = {'deleted_count': len(product_ids)}
                elif operation_type == 'assign_category':
                    category_id = data.get('category_id')
                    ProductProxy.objects.filter(id__in=product_ids).update(category_id=category_id)
                    result = {'updated_count': len(product_ids)}
                else:
                    return Response(
                        {'error': 'Invalid operation type'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                return Response(result, status=status.HTTP_200_OK)

            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], permission_classes=[CanManageProducts])
    def create_with_variants(self, request):
        """
        Create a product with variants in one request
        """
        serializer = ProductCreateWithVariantsSerializer(
            data=request.data,
            context={'request': request}
        )
        if serializer.is_valid():
            result = serializer.save()
            return Response({
                'product': ProductStaffSerializer(result['product']).data,
                'variants': ProductVariantStaffSerializer(result['variants'], many=True).data
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['patch'], permission_classes=[CanManageProducts])
    def change_status(self, request, pk=None):
        """
        Change product active status
        """
        product = self.get_object()
        is_active = request.data.get('is_active')

        if is_active is not None:
            old_status = product.is_active
            product.is_active = is_active
            product.save()

            # Log the status change
            AuditService.log_product_action(
                product=product,
                staff_user=request.user,
                action='STATUS_CHANGE',
                changes={
                    'old_status': old_status,
                    'new_status': is_active
                },
                request=request
            )

            return Response({
                'message': 'Product status updated successfully',
                'is_active': product.is_active
            })

        return Response(
            {'error': 'is_active field is required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=False, methods=['get'], permission_classes=[CanManageProducts])
    def analytics(self, request):
        """
        Get product analytics data
        """
        analytics_data = ProductService.get_product_analytics()

        # Add additional analytics
        analytics_data.update({
            'top_categories': list(
                CategoryProxy.objects.annotate(
                    products_count=Count('products')
                ).order_by('-products_count')[:5].values('title', 'products_count')
            ),
            'top_brands': list(
                BrandProxy.objects.annotate(
                    products_count=Count('product')
                ).order_by('-products_count')[:5].values('title', 'products_count')
            ),
            'stock_alerts': list(
                ProductVariantProxy.objects.filter(
                    stock_qty__lte=10, stock_qty__gt=0
                ).select_related('product').values(
                    'product__title', 'sku', 'stock_qty'
                )[:10]
            )
        })

        serializer = ProductAnalyticsSerializer(analytics_data)
        return Response(serializer.data)

    @action(detail=True, methods=['get'], permission_classes=[CanManageProducts])
    def variants(self, request, pk=None):
        """
        Get all variants for a specific product, ordered by their order field
        """
        product = self.get_object()

        # Get all variants for this product
        variants = ProductVariantProxy.objects.filter(
            product=product
        ).select_related('price_label').prefetch_related(
            'product_image', 'attribute_value'
        ).order_by('order')

        # Apply filtering if provided
        is_active = request.query_params.get('is_active')
        if is_active is not None:
            is_active_bool = is_active.lower() in ['true', '1']
            variants = variants.filter(is_active=is_active_bool)

        # Serialize the data
        serializer = ProductVariantStaffSerializer(variants, many=True)

        return Response({
            'product_id': product.id,
            'product_title': product.title,
            'variants_count': variants.count(),
            'variants': serializer.data
        })


class CategoryStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff category management
    """
    queryset = CategoryProxy.objects.all().select_related('parent')
    serializer_class = CategoryStaffSerializer
    permission_classes = [CanManageCategories]
    # filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    # filterset_class = CategoryStaffFilter
    search_fields = ['title']
    ordering_fields = ['title', 'level']
    ordering = ['tree_id', 'lft']  # MPTT ordering

    def get_queryset(self):
        queryset = super().get_queryset()

        # Add product counts
        queryset = queryset.annotate(
            products_count=Count('products'),
            # children_count=Count('children')
        )

        return queryset

    @action(detail=True, methods=['post'], permission_classes=[CanManageCategories])
    def move(self, request, pk=None):
        """
        Move category to a new parent
        """
        category = self.get_object()
        serializer = CategoryMoveSerializer(data=request.data)

        if serializer.is_valid():
            new_parent_id = serializer.validated_data.get('new_parent_id')

            try:
                moved_category = CategoryService.move_category(
                    category_id=category.id,
                    new_parent_id=new_parent_id,
                    staff_user=request.user,
                    request=request
                )

                return Response({
                    'message': 'Category moved successfully',
                    'category': CategoryStaffSerializer(moved_category).data
                })

            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def tree(self, request):
        """
        Get category tree structure
        """
        categories = CategoryProxy.objects.all().order_by('tree_id', 'lft')
        serializer = CategoryStaffSerializer(categories, many=True)
        return Response(serializer.data)


class BrandStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff brand management
    """
    queryset = BrandProxy.objects.all().prefetch_related('product_types')
    serializer_class = BrandStaffSerializer
    permission_classes = [CanManageBrands]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = BrandStaffFilter
    search_fields = ['title']
    ordering_fields = ['title']
    ordering = ['title']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Add product counts
        queryset = queryset.annotate(
            products_count=Count('product')
        )

        return queryset


class ProductTypeStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff product type management
    """
    queryset = ProductTypeProxy.objects.all().select_related('parent').prefetch_related('attribute')
    serializer_class = ProductTypeStaffSerializer
    permission_classes = [CanManageProductTypes]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = ProductTypeStaffFilter
    search_fields = ['title']
    ordering_fields = ['title']
    ordering = ['title']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Add counts
        queryset = queryset.annotate(
            attributes_count=Count('attribute'),
            products_count=Count('product_type')
        )

        return queryset

    @action(detail=True, methods=['get'], permission_classes=[CanManageProductTypes])
    def attributes(self, request, pk=None):
        """
        Get attributes associated with this product type
        """
        product_type = self.get_object()
        associations = ProductTypeAttributeProxy.objects.filter(
            product_type=product_type
        ).select_related('attribute')

        serializer = ProductTypeAttributeStaffSerializer(associations, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'], permission_classes=[CanManageProductTypes])
    def attribute_values(self, request, pk=None):
        """
        Get all attribute values with their attributes for this product type
        Optimized endpoint without query parameters

        Returns:
        {
            "product_type_id": 1,
            "product_type_title": "Electronics",
            "attributes": [
                {
                    "attribute_id": 1,
                    "attribute_title": "Color",
                    "values": [
                        {
                            "id": 1,
                            "attribute_value": "Red",
                            "is_active": true,
                            "for_filtering": true
                        }
                    ]
                }
            ],
            "total_attributes": 1,
            "total_values": 1
        }
        """
        product_type = self.get_object()

        # Get all attribute values for this product type with optimized query
        attribute_values = AttributeValueProxy.objects.filter(
            attribute__product_type_attribute_a__product_type=product_type
        ).select_related('attribute').order_by('attribute__title', 'attribute_value')

        # Group by attribute for better response structure
        result = {}
        for attr_value in attribute_values:
            attribute_title = attr_value.attribute.title
            if attribute_title not in result:
                result[attribute_title] = {
                    'attribute_id': attr_value.attribute.id,
                    'attribute_title': attribute_title,
                    'values': []
                }

            # Handle for_filtering field gracefully (might not exist in all databases)
            # for_filtering_value = getattr(attr_value, 'for_filtering', True)  # Default to True if field doesn't exist

            result[attribute_title]['values'].append({
                'id': attr_value.id,
                'attribute_value': attr_value.attribute_value,
                'is_active': attr_value.is_active,
                # 'for_filtering': for_filtering_value
            })

        # Convert to list format for consistent API response
        response_data = list(result.values())

        # Prepare response data
        response_data_dict = {
            'product_type_id': product_type.id,
            'product_type_title': product_type.title,
            'attributes': response_data,
            'total_attributes': len(response_data),
            'total_values': sum(len(attr['values']) for attr in response_data)
        }

        # Use serializer for consistent response format
        serializer = ProductTypeAttributeValuesResponseSerializer(response_data_dict)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[CanManageProductTypes])
    def associate_attributes(self, request, pk=None):
        """
        Associate attributes with the product type
        """
        product_type = self.get_object()
        serializer = BulkAttributeAssociationSerializer(data=request.data)

        if serializer.is_valid():
            try:
                associations = ProductService.associate_attributes_bulk(
                    product_type_id=product_type.id,
                    attribute_data=serializer.validated_data['attributes'],
                    staff_user=request.user,
                    request=request
                )

                return Response({
                    'message': 'Attributes associated successfully',
                    'associations_count': len(associations)
                })

            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'], permission_classes=[CanManageProductTypes])
    def brands(self, request, pk=None):
        """
        Get brands associated with this product type
        """
        product_type = self.get_object()
        associations = BrandProductTypeProxy.objects.filter(
            product_type=product_type
        ).select_related('brand')

        serializer = BrandProductTypeStaffSerializer(associations, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[CanManageProductTypes])
    def associate_brands(self, request, pk=None):
        """
        Associate brands with the product type
        """
        product_type = self.get_object()
        serializer = BulkProductTypeBrandSerializer(data=request.data)

        if serializer.is_valid():
            try:
                result = ProductTypeBrandService.associate_brands_bulk(
                    product_type_id=product_type.id,
                    brand_ids=serializer.validated_data['brand_ids'],
                    staff_user=request.user,
                    request=request
                )

                return Response({
                    'message': f'Associated {result["total_created"]} brands with product type',
                    'created_count': result['total_created'],
                    'existing_count': result['total_existing'],
                    'associations': BrandProductTypeStaffSerializer(result['created_associations'], many=True).data
                })

            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], permission_classes=[CanManageProductTypes])
    def remove_brands(self, request, pk=None):
        """
        Remove brand associations from the product type
        """
        product_type = self.get_object()
        serializer = BulkProductTypeBrandSerializer(data=request.data)

        if serializer.is_valid():
            try:
                result = ProductTypeBrandService.remove_brands_bulk(
                    product_type_id=product_type.id,
                    brand_ids=serializer.validated_data['brand_ids'],
                    staff_user=request.user,
                    request=request
                )

                return Response({
                    'message': f'Removed {result["removed_count"]} brand associations from product type',
                    'removed_count': result['removed_count']
                })

            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AttributeStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff attribute management
    """
    queryset = AttributeProxy.objects.all().prefetch_related('attribute_value', 'product_type_attribute')
    serializer_class = AttributeStaffSerializer
    permission_classes = [CanManageAttributes]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = AttributeStaffFilter
    search_fields = ['title']
    ordering_fields = ['title']
    ordering = ['title']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Add counts
        queryset = queryset.annotate(
            values_count=Count('attribute_value')
        )

        return queryset


class AttributeValueStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff attribute value management
    """
    queryset = AttributeValueProxy.objects.all().select_related('attribute')
    serializer_class = AttributeValueStaffSerializer
    permission_classes = [CanManageAttributes]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = AttributeValueStaffFilter
    search_fields = ['attribute_value', 'attribute__title']
    ordering_fields = ['attribute_value', 'attribute__title']
    ordering = ['attribute__title', 'attribute_value']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Add product counts
        queryset = queryset.annotate(
            products_count=Count('product_attr_value')
        )

        return queryset

    @action(detail=False, methods=['post'], permission_classes=[CanManageAttributes])
    def bulk_create(self, request):
        """
        Bulk create attribute values
        """
        attribute_id = request.data.get('attribute_id')
        values = request.data.get('values', [])

        if not attribute_id or not values:
            return Response(
                {'error': 'attribute_id and values are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                created_values = []
                for value_text in values:
                    value, created = AttributeValueProxy.objects.get_or_create(
                        attribute_id=attribute_id,
                        attribute_value=value_text
                    )
                    if created:
                        created_values.append(value)

                return Response({
                    'message': f'Created {len(created_values)} attribute values',
                    'created_count': len(created_values),
                    'values': AttributeValueStaffSerializer(created_values, many=True).data
                })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class ProductVariantStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff product variant management
    """
    queryset = ProductVariantProxy.objects.all().select_related(
        'product', 'price_label'
    ).prefetch_related('product_image', 'attribute_value')
    serializer_class = ProductVariantStaffSerializer
    permission_classes = [CanManageProducts]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = ProductVariantStaffFilter
    search_fields = ['sku', 'product__title']
    ordering_fields = ['price', 'stock_qty', 'created_at', 'order']
    ordering = ['product', 'order']

    @action(detail=True, methods=['patch'], permission_classes=[IsInventoryManager])
    def update_stock(self, request, pk=None):
        """
        Update stock quantity (for inventory managers)
        """
        variant = self.get_object()
        new_stock = request.data.get('stock_qty')

        if new_stock is not None:
            old_stock = variant.stock_qty
            variant.stock_qty = new_stock
            variant.save()

            # Log the stock update
            AuditService.log_product_action(
                product=variant.product,
                staff_user=request.user,
                action='VARIANT_UPDATE',
                changes={
                    'variant_sku': variant.sku,
                    'old_stock': old_stock,
                    'new_stock': new_stock
                },
                request=request
            )

            return Response({
                'message': 'Stock updated successfully',
                'sku': variant.sku,
                'stock_qty': variant.stock_qty
            })

        return Response(
            {'error': 'stock_qty is required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=True, methods=['get'], permission_classes=[CanManageProducts])
    def attribute_values(self, request, pk=None):
        """
        Get all associated attribute values for a specific product variant
        """
        variant = self.get_object()

        # Get all attribute value associations for this variant
        associations = ProductVariantAttributeValueProxy.objects.filter(
            product_variant=variant
        ).select_related(
            'attribute_value__attribute'
        ).order_by('order', 'attribute_value__attribute__title')

        # Apply filtering if provided
        is_active = request.query_params.get('is_active')
        if is_active is not None:
            is_active_bool = is_active.lower() in ['true', '1']
            associations = associations.filter(is_active=is_active_bool)

        # Apply attribute filtering if provided
        attribute_id = request.query_params.get('attribute')
        if attribute_id:
            associations = associations.filter(attribute_value__attribute_id=attribute_id)

        # Serialize the data
        serializer = ProductVariantAttributeValueDetailSerializer(associations, many=True)

        return Response({
            # 'variant_sku': variant.sku,
            'variant_id': variant.id,
            'product_title': variant.product.title,
            'attribute_values_count': associations.count(),
            'attribute_values': serializer.data
        })

    @action(detail=False, methods=['patch'], permission_classes=[CanManageProducts])
    def reorder_drag_drop(self, request):
        """
        Reorder product variants for drag-and-drop frontend
        Expected data: {
            'product_id': 1,
            'ordered_ids': [3, 1, 2]  # Array of variant IDs in new order
        }
        """
        product_id = request.data.get('product_id')
        ordered_ids = request.data.get('ordered_ids', [])

        if not product_id:
            return Response(
                {'error': 'product_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not ordered_ids:
            return Response(
                {'error': 'ordered_ids is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                # Verify all IDs belong to the specified product
                variants = ProductVariantProxy.objects.filter(
                    product_id=product_id,
                    id__in=ordered_ids
                )

                if variants.count() != len(ordered_ids):
                    return Response(
                        {'error': 'Some variant IDs do not exist or do not belong to this product'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Update order based on position in ordered_ids array
                for new_order, variant_id in enumerate(ordered_ids, start=1):
                    ProductVariantProxy.objects.filter(
                        id=variant_id
                    ).update(order=new_order)

                # Get updated variants
                updated_variants = ProductVariantProxy.objects.filter(
                    product_id=product_id
                ).order_by('order')

                serializer = self.get_serializer(updated_variants, many=True)
                return Response({
                    'message': f'Reordered {len(ordered_ids)} variants',
                    'variants': serializer.data
                })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['patch'], permission_classes=[CanManageProducts])
    def reorder(self, request, pk=None):
        """
        Manually reorder a single product variant
        Expected data: {'new_order': 2}
        """
        variant = self.get_object()
        new_order = request.data.get('new_order')

        if new_order is None:
            return Response(
                {'error': 'new_order is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            new_order = int(new_order)
            if new_order < 1:
                return Response(
                    {'error': 'new_order must be greater than 0'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except (ValueError, TypeError):
            return Response(
                {'error': 'new_order must be a valid integer'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                # Get all variants for the same product
                variants = ProductVariantProxy.objects.filter(
                    product=variant.product
                ).order_by('order')

                # Convert to list for easier manipulation
                variants_list = list(variants)

                # Remove the current variant from its position
                current_variant = None
                for i, var in enumerate(variants_list):
                    if var.id == variant.id:
                        current_variant = variants_list.pop(i)
                        break

                # Insert at new position (adjust for 0-based indexing)
                insert_position = min(new_order - 1, len(variants_list))
                variants_list.insert(insert_position, current_variant)

                # Update order for all variants
                for i, var in enumerate(variants_list):
                    var.order = i + 1
                    var.save()

                # Return updated variant
                variant.refresh_from_db()
                serializer = self.get_serializer(variant)
                return Response({
                    'message': f'Reordered variant to position {new_order}',
                    'variant': serializer.data
                })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['patch'], permission_classes=[CanManageProducts])
    def bulk_update_order(self, request):
        """
        Bulk update order of product variants
        Expected data format: {'order_updates': [{'id': 1, 'order': 1}, {'id': 2, 'order': 2}, ...]}
        """
        serializer = BulkOrderUpdateSerializer(data=request.data)

        if serializer.is_valid():
            order_updates = serializer.validated_data['order_updates']

            try:
                with transaction.atomic():
                    updated_count = 0
                    for update in order_updates:
                        variant_id = update['id']
                        new_order = update['order']

                        ProductVariantProxy.objects.filter(
                            id=variant_id
                        ).update(order=new_order)
                        updated_count += 1

                    return Response({
                        'message': f'Updated order for {updated_count} variants',
                        'updated_count': updated_count
                    })

            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ProductImageStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff product image management
    """
    queryset = ProductImageProxy.objects.all().select_related('product_variant')
    serializer_class = ProductImageStaffSerializer
    permission_classes = [CanManageProducts]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['alternative_text', 'product_variant__sku', 'product_variant__product__title']
    filterset_class = ProductImageStaffFilter
    ordering_fields = ['order', 'product_variant']
    ordering = ['product_variant', 'order']

    def perform_create(self, serializer):
        image = serializer.save()

        # Log the image addition
        AuditService.log_product_action(
            product=image.product_variant.product,
            staff_user=self.request.user,
            action='IMAGE_ADD',
            changes={
                'variant_sku': image.product_variant.sku,
                'image_alt': image.alternative_text
            },
            request=self.request
        )

    @action(detail=False, methods=['patch'], permission_classes=[CanManageProducts])
    def reorder_drag_drop(self, request):
        """
        Reorder product images for drag-and-drop frontend
        Expected data: {
            'product_variant_id': 1,
            'ordered_ids': [3, 1, 2]  # Array of image IDs in new order
        }
        """
        product_variant_id = request.data.get('product_variant_id')
        ordered_ids = request.data.get('ordered_ids', [])

        if not product_variant_id:
            return Response(
                {'error': 'product_variant_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not ordered_ids:
            return Response(
                {'error': 'ordered_ids is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                # Verify all IDs belong to the specified product variant
                images = ProductImageProxy.objects.filter(
                    product_variant_id=product_variant_id,
                    id__in=ordered_ids
                )

                if images.count() != len(ordered_ids):
                    return Response(
                        {'error': 'Some image IDs do not exist or do not belong to this product variant'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Update order based on position in ordered_ids array
                for new_order, image_id in enumerate(ordered_ids, start=1):
                    ProductImageProxy.objects.filter(
                        id=image_id
                    ).update(order=new_order)

                # Get updated images
                updated_images = ProductImageProxy.objects.filter(
                    product_variant_id=product_variant_id
                ).order_by('order')

                serializer = self.get_serializer(updated_images, many=True)
                return Response({
                    'message': f'Reordered {len(ordered_ids)} images',
                    'images': serializer.data
                })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['patch'], permission_classes=[CanManageProducts])
    def reorder(self, request, pk=None):
        """
        Manually reorder a single product image
        Expected data: {'new_order': 2}
        """
        image = self.get_object()
        new_order = request.data.get('new_order')

        if new_order is None:
            return Response(
                {'error': 'new_order is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            new_order = int(new_order)
            if new_order < 1:
                return Response(
                    {'error': 'new_order must be greater than 0'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except (ValueError, TypeError):
            return Response(
                {'error': 'new_order must be a valid integer'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                # Get all images for the same product variant
                images = ProductImageProxy.objects.filter(
                    product_variant=image.product_variant
                ).order_by('order')

                # Convert to list for easier manipulation
                images_list = list(images)

                # Remove the current image from its position
                current_image = None
                for i, img in enumerate(images_list):
                    if img.id == image.id:
                        current_image = images_list.pop(i)
                        break

                # Insert at new position (adjust for 0-based indexing)
                insert_position = min(new_order - 1, len(images_list))
                images_list.insert(insert_position, current_image)

                # Update order for all images
                for i, img in enumerate(images_list):
                    img.order = i + 1
                    img.save()

                # Return updated image
                image.refresh_from_db()
                serializer = self.get_serializer(image)
                return Response({
                    'message': f'Reordered image to position {new_order}',
                    'image': serializer.data
                })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class ReviewStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff review management (read-only with moderation)
    """
    queryset = ReviewProxy.objects.all().select_related('product', 'customer')
    serializer_class = ReviewStaffSerializer
    permission_classes = [CanManageReviews]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = ReviewStaffFilter
    search_fields = ['title', 'description', 'product__title']
    ordering_fields = ['rating', 'posted_at']
    ordering = ['-posted_at']
    http_method_names = ['get', 'delete']  # Read-only with delete for moderation

    @action(detail=True, methods=['post'], permission_classes=[CanManageReviews])
    def moderate(self, request, pk=None):
        """
        Moderate review (placeholder for future moderation features)
        """
        review = self.get_object()
        action = request.data.get('action')  # 'approve', 'reject', 'flag'

        # This is a placeholder - you can extend with actual moderation logic
        return Response({
            'message': f'Review {action} action recorded',
            'review_id': review.id
        })


class DiscountStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff discount management
    """
    queryset = DiscountProxy.objects.all().prefetch_related('product_variants')
    serializer_class = DiscountStaffSerializer
    permission_classes = [CanManageDiscounts]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = DiscountStaffFilter
    search_fields = ['name']
    ordering_fields = ['name', 'start_date', 'end_date', 'discount_percentage']
    ordering = ['-start_date']

    @action(detail=True, methods=['post'], permission_classes=[CanManageDiscounts])
    def apply_to_variants(self, request, pk=None):
        """
        Apply discount to specific product variants
        """
        discount = self.get_object()
        variant_ids = request.data.get('variant_ids', [])

        if not variant_ids:
            return Response(
                {'error': 'variant_ids is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            variants = ProductVariantProxy.objects.filter(id__in=variant_ids)
            discount.product_variants.add(*variants)

            return Response({
                'message': f'Discount applied to {len(variants)} variants',
                'applied_count': len(variants)
            })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class ProductAuditViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing product audit logs
    """
    queryset = ProductAudit.objects.all().select_related('product', 'staff_user')
    serializer_class = ProductAuditSerializer
    permission_classes = [CanManageProducts]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ['timestamp']
    ordering = ['-timestamp']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by product if specified
        product_id = self.request.query_params.get('product_id')
        if product_id:
            queryset = queryset.filter(product_id=product_id)

        # Filter by staff user if specified
        staff_user_id = self.request.query_params.get('staff_user_id')
        if staff_user_id:
            queryset = queryset.filter(staff_user_id=staff_user_id)

        # Filter by action if specified
        action = self.request.query_params.get('action')
        if action:
            queryset = queryset.filter(action=action)

        return queryset


class BulkOperationViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing bulk operation status
    """
    queryset = BulkProductOperation.objects.all().select_related('staff_user')
    serializer_class = BulkOperationSerializer
    permission_classes = [CanManageProducts]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ['started_at']
    ordering = ['-started_at']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by staff user if specified
        staff_user_id = self.request.query_params.get('staff_user_id')
        if staff_user_id:
            queryset = queryset.filter(staff_user_id=staff_user_id)

        # Filter by status if specified
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset


class AssociationManagementViewSet(viewsets.ModelViewSet):
    """
    ModelViewSet for managing product type attribute associations
    Provides standard REST endpoints plus enhanced admin interface functionality
    """
    queryset = ProductTypeAttributeProxy.objects.all().select_related('product_type', 'attribute')
    serializer_class = ProductTypeAttributeStaffSerializer
    permission_classes = [CanManageAttributes]

    def update(self, request, *args, **kwargs):
        """
        Update product type attribute association
        Standard REST endpoint: PATCH /api/staff/products/associations/{association_id}/
        """
        partial = kwargs.pop('partial', False)
        instance = self.get_object()

        # Store original values for audit logging
        original_is_filterable = instance.is_filterable
        original_is_option_selector = instance.is_option_selector

        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        # Save the updated association
        updated_association = serializer.save()

        # Log the action
        AuditService.log_action(
            action='ASSOCIATION_UPDATE',
            staff_user=request.user,
            details={
                'association_id': instance.id,
                'product_type_id': updated_association.product_type.id,
                'attribute_id': updated_association.attribute.id,
                'changes': {
                    'is_filterable': {
                        'from': original_is_filterable,
                        'to': updated_association.is_filterable
                    },
                    'is_option_selector': {
                        'from': original_is_option_selector,
                        'to': updated_association.is_option_selector
                    }
                }
            },
            request=request
        )

        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        """
        Delete product type attribute association
        Standard REST endpoint: DELETE /api/staff/products/associations/{association_id}/
        """
        instance = self.get_object()

        # Store details for audit logging before deletion
        product_type_id = instance.product_type.id
        attribute_id = instance.attribute.id
        product_type_title = instance.product_type.title
        attribute_title = instance.attribute.title
        association_id = instance.id

        # Perform the deletion
        self.perform_destroy(instance)

        # Log the action
        AuditService.log_action(
            action='ASSOCIATION_DELETE',
            staff_user=request.user,
            details={
                'association_id': association_id,
                'product_type_id': product_type_id,
                'attribute_id': attribute_id,
                'product_type_title': product_type_title,
                'attribute_title': attribute_title
            },
            request=request
        )

        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=False, methods=['get'])
    def product_type_attributes(self, request):
        """
        Get product type attributes with search and filtering
        Enhanced admin interface functionality
        """
        product_type_id = request.query_params.get('product_type_id')
        search = request.query_params.get('search', '')

        if product_type_id:
            # Get existing associations for the product type
            associations = ProductTypeAttributeProxy.objects.filter(
                product_type_id=product_type_id
            ).select_related('attribute')

            if search:
                associations = associations.filter(
                    attribute__title__icontains=search
                )

            serializer = ProductTypeAttributeStaffSerializer(associations, many=True)
            return Response(serializer.data)

        # Return all product type attributes if no specific product type
        associations = ProductTypeAttributeProxy.objects.all().select_related(
            'product_type', 'attribute'
        )

        if search:
            associations = associations.filter(
                Q(product_type__title__icontains=search) |
                Q(attribute__title__icontains=search)
            )

        # Paginate results
        paginated_data = paginate_queryset(associations, request)
        serializer = ProductTypeAttributeStaffSerializer(
            paginated_data['items'], many=True
        )

        return Response({
            'results': serializer.data,
            'pagination': paginated_data['pagination']
        })

    @action(detail=False, methods=['post'])
    def save_association(self, request):
        """
        Save or update product type attribute association
        Enhanced admin interface functionality
        """
        serializer = ProductTypeAttributeStaffSerializer(data=request.data)

        if serializer.is_valid():
            association = serializer.save()

            # Log the action
            AuditService.log_action(
                action='ASSOCIATION_SAVE',
                staff_user=request.user,
                details={
                    'product_type_id': association.product_type.id,
                    'attribute_id': association.attribute.id,
                    'is_filterable': association.is_filterable,
                    'is_option_selector': association.is_option_selector
                },
                request=request
            )

            return Response(
                ProductTypeAttributeStaffSerializer(association).data,
                status=status.HTTP_201_CREATED
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    # Legacy methods for backward compatibility (deprecated)
    @action(detail=False, methods=['delete'])
    def delete_association(self, request):
        """
        Delete product type attribute association (DEPRECATED)
        Use DELETE /api/staff/products/associations/{association_id}/ instead
        """
        association_id = request.data.get('association_id')

        if not association_id:
            return Response(
                {'error': 'association_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            association = ProductTypeAttributeProxy.objects.get(id=association_id)
            association.delete()

            return Response({
                'message': 'Association deleted successfully'
            })

        except ProductTypeAttributeProxy.DoesNotExist:
            return Response(
                {'error': 'Association not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['patch'])
    def update_association(self, request):
        """
        Update product type attribute association (DEPRECATED)
        Use PATCH /api/staff/products/associations/{association_id}/ instead
        """
        association_id = request.data.get('association_id')

        if not association_id:
            return Response(
                {'error': 'association_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            association = ProductTypeAttributeProxy.objects.get(id=association_id)
        except ProductTypeAttributeProxy.DoesNotExist:
            return Response(
                {'error': 'Association not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Create a copy of request data without association_id for serializer
        update_data = request.data.copy()
        update_data.pop('association_id', None)

        # Use the serializer for partial update
        serializer = ProductTypeAttributeStaffSerializer(
            association,
            data=update_data,
            partial=True
        )

        if serializer.is_valid():
            # Store original values for audit logging
            original_is_filterable = association.is_filterable
            original_is_option_selector = association.is_option_selector

            # Save the updated association
            updated_association = serializer.save()

            # Log the action
            AuditService.log_action(
                action='ASSOCIATION_UPDATE',
                staff_user=request.user,
                details={
                    'association_id': association_id,
                    'product_type_id': updated_association.product_type.id,
                    'attribute_id': updated_association.attribute.id,
                    'changes': {
                        'is_filterable': {
                            'from': original_is_filterable,
                            'to': updated_association.is_filterable
                        },
                        'is_option_selector': {
                            'from': original_is_option_selector,
                            'to': updated_association.is_option_selector
                        }
                    }
                },
                request=request
            )

            return Response(
                ProductTypeAttributeStaffSerializer(updated_association).data,
                status=status.HTTP_200_OK
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class BrandProductTypeStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing brand-product type associations
    """
    queryset = BrandProductTypeProxy.objects.all().select_related('brand', 'product_type')
    serializer_class = BrandProductTypeStaffSerializer
    permission_classes = [CanManageBrands]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = BrandProductTypeStaffFilter
    search_fields = ['brand__title', 'product_type__title']
    ordering_fields = ['brand__title', 'product_type__title']
    ordering = ['brand__title', 'product_type__title']

    @action(detail=False, methods=['post'], permission_classes=[CanManageBrands])
    def bulk_associate(self, request):
        """
        Bulk associate product types with a brand
        """
        serializer = BulkBrandProductTypeSerializer(data=request.data)

        if serializer.is_valid():
            brand_id = serializer.validated_data['brand_id']
            product_type_ids = serializer.validated_data['product_type_ids']

            try:
                with transaction.atomic():
                    brand = BrandProxy.objects.get(id=brand_id)
                    created_associations = []

                    for product_type_id in product_type_ids:
                        association, created = BrandProductTypeProxy.objects.get_or_create(
                            brand=brand,
                            product_type_id=product_type_id
                        )
                        if created:
                            created_associations.append(association)

                    # Log the action
                    AuditService.log_action(
                        action='BULK_BRAND_PRODUCT_TYPE_ASSOCIATION',
                        staff_user=request.user,
                        details={
                            'brand_id': brand_id,
                            'product_type_ids': product_type_ids,
                            'created_count': len(created_associations)
                        },
                        request=request
                    )

                    return Response({
                        'message': f'Associated {len(created_associations)} product types with brand',
                        'created_count': len(created_associations),
                        'associations': BrandProductTypeStaffSerializer(created_associations, many=True).data
                    })

            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ProductAttributeValueStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing product-level attribute value associations
    """
    queryset = ProductAttributeValueProxy.objects.all().select_related(
        'product', 'attribute_value__attribute'
    )
    serializer_class = ProductAttributeValueStaffSerializer
    permission_classes = [CanManageAttributes]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = ProductAttributeValueStaffFilter
    search_fields = ['product__title', 'attribute_value__attribute_value']
    ordering_fields = ['product__title', 'attribute_value__attribute__title']
    ordering = ['product__title', 'attribute_value__attribute__title']

    @action(detail=False, methods=['post'], permission_classes=[CanManageAttributes])
    def bulk_associate(self, request):
        """
        Bulk associate attribute values with a product
        """
        serializer = BulkProductAttributeValueSerializer(data=request.data)

        if serializer.is_valid():
            product_id = serializer.validated_data['product_id']
            attribute_value_ids = serializer.validated_data['attribute_value_ids']

            try:
                with transaction.atomic():
                    product = ProductProxy.objects.get(id=product_id)
                    created_associations = []

                    for attribute_value_id in attribute_value_ids:
                        association, created = ProductAttributeValueProxy.objects.get_or_create(
                            product=product,
                            attribute_value_id=attribute_value_id
                        )
                        if created:
                            created_associations.append(association)

                    return Response({
                        'message': f'Associated {len(created_associations)} attribute values with product',
                        'created_count': len(created_associations),
                        'associations': ProductAttributeValueStaffSerializer(created_associations, many=True).data
                    })

            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ProductVariantAttributeValueStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing product variant attribute value associations
    """
    queryset = ProductVariantAttributeValueProxy.objects.all().select_related(
        'product_variant__product', 'attribute_value__attribute'
    )
    serializer_class = ProductVariantAttributeValueStaffSerializer
    permission_classes = [CanManageAttributes]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = ProductVariantAttributeValueStaffFilter
    search_fields = ['product_variant__sku', 'attribute_value__attribute_value']
    ordering_fields = ['product_variant__sku', 'attribute_value__attribute__title', 'order']
    ordering = ['product_variant__sku', 'order', 'attribute_value__attribute__title']

    @action(detail=False, methods=['post'], permission_classes=[CanManageAttributes])
    def bulk_associate(self, request):
        """
        Bulk associate attribute values with a product variant
        """
        serializer = BulkVariantAttributeValueSerializer(data=request.data)

        if serializer.is_valid():
            product_variant_id = serializer.validated_data['product_variant_id']
            attribute_value_ids = serializer.validated_data['attribute_value_ids']

            try:
                with transaction.atomic():
                    product_variant = ProductVariantProxy.objects.get(id=product_variant_id)
                    created_associations = []

                    for attribute_value_id in attribute_value_ids:
                        # Check if association already exists
                        existing_association = ProductVariantAttributeValueProxy.objects.filter(
                            product_variant=product_variant,
                            attribute_value_id=attribute_value_id
                        ).first()

                        if not existing_association:
                            # Get the next order number for this product variant
                            max_order = ProductVariantAttributeValueProxy.objects.filter(
                                product_variant=product_variant
                            ).aggregate(max_order=Max('order'))['max_order'] or 0
                            next_order = max_order + 1

                            # Create new association with explicit order
                            association = ProductVariantAttributeValueProxy(
                                product_variant=product_variant,
                                attribute_value_id=attribute_value_id,
                                is_active=True,
                                order=next_order
                            )
                            association.save()
                            created_associations.append(association)

                    # Log the action
                    # AuditService.log_product_action(
                    #     product=product_variant.product,
                    #     staff_user=request.user,
                    #     action='VARIANT_ATTRIBUTE_ASSOCIATION',
                    #     changes={
                    #         'variant_sku': product_variant.sku,
                    #         'attribute_value_ids': attribute_value_ids,
                    #         'created_count': len(created_associations)
                    #     },
                    #     request=request
                    # )

                    return Response({
                        'message': f'Associated {len(created_associations)} attribute values with variant',
                        'created_count': len(created_associations),
                        'associations': ProductVariantAttributeValueStaffSerializer(created_associations,
                                                                                    many=True).data
                    })

            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['patch'], permission_classes=[CanManageAttributes])
    def bulk_update_status(self, request):
        """
        Bulk update active status of variant attribute associations
        """
        association_ids = request.data.get('association_ids', [])
        is_active = request.data.get('is_active', True)

        if not association_ids:
            return Response(
                {'error': 'association_ids is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            updated_count = ProductVariantAttributeValueProxy.objects.filter(
                id__in=association_ids
            ).update(is_active=is_active)

            return Response({
                'message': f'Updated {updated_count} associations',
                'updated_count': updated_count
            })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['patch'], permission_classes=[CanManageAttributes])
    def bulk_update_order(self, request):
        """
        Bulk update order of variant attribute associations
        Expected data format: {'order_updates': [{'id': 1, 'order': 1}, {'id': 2, 'order': 2}, ...]}
        """
        serializer = BulkOrderUpdateSerializer(data=request.data)

        if serializer.is_valid():
            order_updates = serializer.validated_data['order_updates']

            try:
                with transaction.atomic():
                    updated_count = 0
                    for update in order_updates:
                        association_id = update['id']
                        new_order = update['order']

                        ProductVariantAttributeValueProxy.objects.filter(
                            id=association_id
                        ).update(order=new_order)
                        updated_count += 1

                    return Response({
                        'message': f'Updated order for {updated_count} associations',
                        'updated_count': updated_count
                    })

            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['patch'], permission_classes=[CanManageAttributes])
    def reorder(self, request, pk=None):
        """
        Manually reorder a single variant attribute association
        Expected data: {'new_order': 2}
        """
        association = self.get_object()
        new_order = request.data.get('new_order')

        if new_order is None:
            return Response(
                {'error': 'new_order is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            new_order = int(new_order)
            if new_order < 1:
                return Response(
                    {'error': 'new_order must be greater than 0'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except (ValueError, TypeError):
            return Response(
                {'error': 'new_order must be a valid integer'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                # Get all associations for the same product variant
                associations = ProductVariantAttributeValueProxy.objects.filter(
                    product_variant=association.product_variant
                ).order_by('order')

                # Convert to list for easier manipulation
                associations_list = list(associations)

                # Remove the current association from its position
                current_association = None
                for i, assoc in enumerate(associations_list):
                    if assoc.id == association.id:
                        current_association = associations_list.pop(i)
                        break

                # Insert at new position (adjust for 0-based indexing)
                insert_position = min(new_order - 1, len(associations_list))
                associations_list.insert(insert_position, current_association)

                # Update order for all associations
                for i, assoc in enumerate(associations_list):
                    assoc.order = i + 1
                    assoc.save()

                # Return updated association
                association.refresh_from_db()
                serializer = self.get_serializer(association)
                return Response({
                    'message': f'Reordered association to position {new_order}',
                    'association': serializer.data
                })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['patch'], permission_classes=[CanManageAttributes])
    def reorder_drag_drop(self, request):
        """
        Reorder variant attribute associations for drag-and-drop frontend
        Expected data: {
            'product_variant_id': 1,
            'ordered_ids': [3, 1, 2]  # Array of association IDs in new order
        }
        """
        product_variant_id = request.data.get('product_variant_id')
        ordered_ids = request.data.get('ordered_ids', [])

        if not product_variant_id:
            return Response(
                {'error': 'product_variant_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not ordered_ids:
            return Response(
                {'error': 'ordered_ids is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                # Verify all IDs belong to the specified product variant
                associations = ProductVariantAttributeValueProxy.objects.filter(
                    product_variant_id=product_variant_id,
                    id__in=ordered_ids
                )

                if associations.count() != len(ordered_ids):
                    return Response(
                        {'error': 'Some association IDs do not exist or do not belong to this product variant'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Update order based on position in ordered_ids array
                for new_order, association_id in enumerate(ordered_ids, start=1):
                    ProductVariantAttributeValueProxy.objects.filter(
                        id=association_id
                    ).update(order=new_order)

                # Get updated associations
                updated_associations = ProductVariantAttributeValueProxy.objects.filter(
                    product_variant_id=product_variant_id
                ).order_by('order')

                serializer = self.get_serializer(updated_associations, many=True)
                return Response({
                    'message': f'Reordered {len(ordered_ids)} associations',
                    'associations': serializer.data
                })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
