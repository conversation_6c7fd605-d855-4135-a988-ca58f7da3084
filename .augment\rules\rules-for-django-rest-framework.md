---
type: "agent_requested"
description: "Best practices when working with Django REST framework"
---
<Views>
- Prefer class-based views (APIView, GenericAPIView, ModelViewSet).
- Prefer ViewSets + @action for custom behavior.
- Use triple-quoted docstrings for major classes and methods.

<Serializers>
- Use ModelSerializer when possible.
- Define read_only_fields, extra_kwargs explicitly.
- Use validate() and validate_<field>() for logic.
- Use SerializerMethodField for computed values.
