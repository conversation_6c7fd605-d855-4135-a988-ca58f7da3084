# Selective Checkout API Endpoints

## Overview

This document details the API endpoints for the selective checkout feature implementation.

## Cart Item Selection Endpoints

### 1. Toggle Item Selection

**Endpoint**: `PATCH /api/cart/{cart_id}/items/{item_id}/select/`
**Method**: PATCH
**Description**: Toggle the selection state of a specific cart item

**Request Body**:

```json
{
  "is_selected": true
}
```

**Response**:

```json
{
  "id": 123,
  "is_selected": true,
  "message": "Item selection updated successfully"
}
```

### 2. Bulk Select Items

**Endpoint**: `POST /api/cart/{cart_id}/items/bulk-select/`
**Method**: POST
**Description**: Select multiple cart items at once

**Request Body**:

```json
{
  "item_ids": [1, 2, 3, 4],
  "select_all": false
}
```

**Response**:

```json
{
  "success": true,
  "updated_count": 4,
  "message": "4 items selected successfully"
}
```

### 3. Bulk Deselect Items

**Endpoint**: `POST /api/cart/{cart_id}/items/bulk-deselect/`
**Method**: POST
**Description**: Deselect multiple cart items at once

**Request Body**:

```json
{
  "item_ids": [1, 2, 3],
  "deselect_all": false
}
```

**Response**:

```json
{
  "success": true,
  "updated_count": 3,
  "message": "3 items deselected successfully"
}
```

### 4. Get Selected Items Summary

**Endpoint**: `GET /api/cart/{cart_id}/selected-summary/`
**Method**: GET
**Description**: Get summary of selected items and totals

**Response**:

```json
{
  "selected_items_count": 3,
  "total_items_count": 5,
  "selected_total_price": 299.97,
  "selected_total_weight": 1500,
  "all_items_selected": false,
  "selected_item_ids": [1, 3, 5]
}
```

## Modified Existing Endpoints

### 1. Enhanced Cart Retrieval

**Endpoint**: `GET /api/cart/{cart_id}/`
**Method**: GET
**Description**: Get cart with selection state and selected totals

**Response**:

```json
{
  "id": "uuid-here",
  "cart_items": [
    {
      "id": 1,
      "product": {...},
      "product_variant": {...},
      "quantity": 2,
    "qty_price": 99.98,
    "is_selected": true,
      "extra_data": {}
    }
  ],
  "customer": "customer-id",
  "cart_weight": 2000,
  "total_price": 199.96,
  "selected_total_price": 99.98,
  "selected_cart_weight": 1000,
  "selected_item_count": 1,
  "item_count": 2,
  "all_items_selected": false,
  "shipping_cost": 15.00,
  "selected_shipping_cost": 15.00,
  /* Note: current backend implementation returns cart.shipping_cost for selected_shipping_cost; per-item proportional splitting is a planned enhancement. */
  "grand_total": 214.96,
  "selected_grand_total": 114.98
}
```

### 2. Enhanced Order Creation

**Endpoint**: `POST /api/orders/`
**Method**: POST
**Description**: Create order with optional selected items

**Request Body**:

```json
{
  "cart_id": "uuid-here",
  "selected_cart_item_ids": [1, 3, 5],
  "selected_address": 1,
  "payment_method": 1,
  "order_status": "Pending"
}
```

**Response**:

```json
{
  "id": 123,
  "customer": {...},
  "placed_at": "2024-01-15T10:30:00Z",
  "payment_status": "Pending",
  "ordered_items": [...],
  "selected_address": {...},
  "order_status": "Pending",
  "payment_method": {...},
  "shipping_cost": 15.00,
  "subtotal": 99.98,
  "total_weight": 1000,
  "total": 114.98,
  "remaining_cart_items": 2
}
```

## Error Responses

### Common Error Codes

- `400 Bad Request`: Invalid request data
- `404 Not Found`: Cart or item not found
- `409 Conflict`: Item out of stock or cart state changed
- `422 Unprocessable Entity`: Validation errors

### Example Error Response

```json
{
  "error": "validation_error",
  "message": "Selected items validation failed",
  "details": {
    "item_ids": ["Item 123 is out of stock"],
    "cart": ["Cart has been modified since last update"]
  }
}
```

## Request/Response Headers

### Required Headers

- `Content-Type: application/json`
- `Authorization: Bearer <token>` (for authenticated requests)

### Response Headers

- `Content-Type: application/json`
- `X-Cart-Version: <timestamp>` (for optimistic locking)

## Rate Limiting

- Selection operations: 100 requests per minute per user
- Bulk operations: 20 requests per minute per user
- Cart retrieval: 200 requests per minute per user

## Postman Collection

A comprehensive Postman collection will be provided with:

- All endpoint examples
- Environment variables setup
- Test scenarios for edge cases
- Authentication setup
