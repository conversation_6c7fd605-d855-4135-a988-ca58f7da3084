# Selective Checkout API Testing Guide

This guide provides step-by-step API testing for the complete selective checkout process using Postman.

## Prerequisites

### Environment Setup

Create a Postman environment with these variables:

```text
base_url: http://127.0.0.1:8000
auth_token: [Your JWT token]
cart_id: [Will be set during testing]
customer_id: [Your customer ID]
address_id: [Test address ID]
payment_method_id: [Test payment method ID]
```

### Authentication

All requests require this header:

```text
Authorization: Bearer {{auth_token}}
Content-Type: application/json
```

## Complete Checkout Flow Testing

### Step 1: Create Cart and Add Items

#### 1.1 Create Empty Cart

**Endpoint:** `POST {{base_url}}/cart/`

**Request Body:**

```json
{}
```

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "customer": null,
  "cart_items": [],
  "total_price": 0.0,
  "item_count": 0,
  "cart_weight": 0
}
```

**Test Script:**

```javascript
pm.test('Cart created successfully', function () {
  pm.response.to.have.status(201)
  var jsonData = pm.response.json()
  pm.expect(jsonData.id).to.exist
  pm.environment.set('cart_id', jsonData.id)
})
```

#### 1.2 Add Items to Cart

**Endpoint:** `POST {{base_url}}/cart/{{cart_id}}/items/`

**Request Body:**

```json
{
  "product_id": 1,
  "product_variant": 1,
  "quantity": 2,
  "extra_data": {}
}
```

**Response:**

```json
{
  "id": 1,
  "product": {
    "id": 1,
    "title": "Test Product",
    "slug": "test-product"
  },
  "product_variant": {
    "id": 1,
    "price": 29.99,
    "stock_qty": 10
  },
  "quantity": 2,
  "qty_price": 59.98,
  "is_selected": true
}
```

**Test Script:**

```javascript
pm.test('Item added successfully', function () {
  pm.response.to.have.status(201)
  var jsonData = pm.response.json()
  pm.expect(jsonData.is_selected).to.be.true
  pm.environment.set('item_id_1', jsonData.id)
})
```

#### 1.3 Add More Items (Repeat for multiple items)

Add 2-3 more items using the same endpoint with different product IDs.

### Step 2: Item Selection Operations

#### 2.1 Get Current Cart State

**Endpoint:** `GET {{base_url}}/cart/{{cart_id}}/`

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "cart_items": [
    {
      "id": 1,
      "quantity": 2,
      "qty_price": 59.98,
      "is_selected": true
    }
  ],
  "total_price": 179.94,
  "selected_total_price": 119.96,
  "item_count": 3,
  "selected_item_count": 2,
  "all_items_selected": false
}
```

#### 2.2 Deselect Specific Item

**Endpoint:** `PATCH {{base_url}}/cart/{{cart_id}}/items/{{item_id_1}}/select/`

**Request Body:**

```json
{
  "is_selected": false
}
```

**Response:**

```json
{
  "id": 1,
  "is_selected": false,
  "message": "Item selection updated successfully"
}
```

#### 2.3 Bulk Select Items

**Endpoint:** `POST {{base_url}}/cart/{{cart_id}}/items/bulk-select/`

**Request Body:**

```json
{
  "item_ids": [1, 2]
}
```

**Response:**

```json
{
  "success": true,
  "updated_count": 2,
  "message": "2 items selected successfully"
}
```

#### 2.4 Get Selected Items Summary

**Endpoint:** `GET {{base_url}}/cart/{{cart_id}}/selected-summary/`

**Response:**

```json
{
  "selected_items_count": 2,
  "total_items_count": 3,
  "selected_total_price": 119.96,
  "selected_total_weight": 1500,
  "all_items_selected": false,
  "selected_item_ids": [1, 2]
}
```

### Step 3: Shipping Calculation

#### 3.1 Calculate Shipping for Selected Items

**Endpoint:** `POST {{base_url}}/cart/{{cart_id}}/calculate-shipping/`

**Request Body:**

```json
{
  "destination_address_id": {{address_id}},
  "get_all_options": false,
  "selected_only": true
}
```

**Response:**

```json
{
  "cart": {
    "id": "550e8400-e29b-41d4-a716-************",
    "selected_total_price": 119.96,
    "selected_shipping_cost": 15.0,
    "selected_grand_total": 134.96,
    "selected_cart_weight": 1500,
    "shipping_cost": 25.0,
    "grand_total": 204.94
  },
  "shipping_calculation": {
    "success": true,
    "message": "Shipping calculated successfully",
    "shipping_cost": 15.0,
    "estimated_delivery_days": "3-5",
    "carrier_name": "Standard Shipping"
  }
}
```

**Test Script:**

```javascript
pm.test('Shipping calculated for selected items', function () {
  pm.response.to.have.status(200)
  var jsonData = pm.response.json()
  pm.expect(jsonData.shipping_calculation.success).to.be.true
  pm.expect(jsonData.cart.selected_shipping_cost).to.exist
  pm.expect(jsonData.cart.selected_grand_total).to.exist
})
```

### Step 4: Order Creation

#### 4.1 Create Order with Selected Items

**Endpoint:** `POST {{base_url}}/orders/`

**Request Body:**

```json
{
  "cart_id": "{{cart_id}}",
  "selected_address": {{address_id}},
  "payment_method": {{payment_method_id}},
  "order_status": "Pending",
  "selected_cart_item_ids": [1, 2]
}
```

**Response:**

```json
{
  "id": 123,
  "customer": {
    "id": 1,
    "first_name": "John",
    "last_name": "Doe"
  },
  "placed_at": "2024-01-15T10:45:00Z",
  "payment_status": "Pending",
  "ordered_items": [
    {
      "id": 1,
      "product_title": "Test Product",
      "quantity": 2,
      "price": 29.99,
      "total_price": 59.98
    }
  ],
  "selected_address": {
    "id": 1,
    "street_name": "123 Test St",
    "city_or_village": "Test City"
  },
  "order_status": "Pending",
  "payment_method": {
    "id": 1,
    "name": "Credit Card"
  },
  "shipping_cost": 15.0,
  "subtotal": 119.96,
  "total": 134.96,
  "remaining_cart_items": 1,
  "cart_still_exists": true
}
```

**Test Script:**

```javascript
pm.test('Order created successfully', function () {
  pm.response.to.have.status(201)
  var jsonData = pm.response.json()
  pm.expect(jsonData.id).to.exist
  pm.expect(jsonData.remaining_cart_items).to.equal(1)
  pm.expect(jsonData.cart_still_exists).to.be.true
  pm.environment.set('order_id', jsonData.id)
})
```

#### 4.2 Verify Cart State After Order

**Endpoint:** `GET {{base_url}}/cart/{{cart_id}}/`

**Expected Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "cart_items": [
    {
      "id": 3,
      "quantity": 1,
      "qty_price": 59.98,
      "is_selected": true
    }
  ],
  "total_price": 59.98,
  "item_count": 1
}
```

**Test Script:**

```javascript
pm.test('Cart contains only unselected items', function () {
  pm.response.to.have.status(200)
  var jsonData = pm.response.json()
  pm.expect(jsonData.cart_items).to.have.lengthOf(1)
  pm.expect(jsonData.item_count).to.equal(1)
})
```

## Edge Case Testing

### Test Case 1: Empty Selection Order

**Endpoint:** `POST {{base_url}}/orders/`

**Request Body:**

```json
{
  "cart_id": "{{cart_id}}",
  "selected_address": {{address_id}},
  "payment_method": {{payment_method_id}},
  "order_status": "Pending"
}
```

**Expected Response:** `400 Bad Request`

```json
{
  "selected_cart_item_ids": [
    "No items are selected for checkout. Please select items to proceed."
  ]
}
```

### Test Case 2: Invalid Item IDs

**Endpoint:** `POST {{base_url}}/cart/{{cart_id}}/items/bulk-select/`

**Request Body:**

```json
{
  "item_ids": [99999, 88888]
}
```

**Expected Response:** `400 Bad Request`

```json
{
  "selected_cart_item_ids": "Invalid cart item IDs: [99999, 88888]"
}
```

### Test Case 3: All Items Selected

**Endpoint:** `POST {{base_url}}/cart/{{cart_id}}/items/bulk-select/`

**Request Body:**

```json
{
  "select_all": true
}
```

**Expected Response:**

```json
{
  "success": true,
  "updated_count": 3,
  "message": "All items selected successfully"
}
```

## Performance Testing

### Bulk Operations Test

Test with 50+ items in cart:

1. Add 50 items to cart
2. Bulk select all items
3. Bulk deselect all items
4. Select specific subset
5. Create order with selected items

**Performance Expectations:**

- Bulk operations: < 500ms
- Shipping calculation: < 3s
- Order creation: < 1s

## Postman Collection Structure

```
Selective Checkout Tests/
├── 01 - Setup/
│   ├── Create Cart
│   ├── Add Item 1
│   ├── Add Item 2
│   └── Add Item 3
├── 02 - Selection Operations/
│   ├── Get Cart State
│   ├── Deselect Item
│   ├── Bulk Select
│   ├── Bulk Deselect
│   └── Get Selection Summary
├── 03 - Shipping/
│   ├── Calculate Selected Shipping
│   └── Calculate All Items Shipping
├── 04 - Order Creation/
│   ├── Create Order (Selected Items)
│   ├── Verify Cart After Order
│   └── Get Order Details
└── 05 - Edge Cases/
    ├── Empty Selection Order
    ├── Invalid Item IDs
    └── Performance Tests
```

## Automated Testing Script

```javascript
// Collection-level pre-request script
pm.globals.set('timestamp', Date.now())

// Collection-level test script
pm.test('Response time is acceptable', function () {
  pm.expect(pm.response.responseTime).to.be.below(5000)
})

pm.test('No server errors', function () {
  pm.expect(pm.response.code).to.not.be.oneOf([500, 502, 503, 504])
})
```

This comprehensive testing guide ensures all aspects of the selective checkout system are properly validated through API testing.
