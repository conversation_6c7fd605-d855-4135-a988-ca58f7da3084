import django_filters
from django.db.models import Q, Count, Sum
from .models import CustomerProxy, AddressProxy


class CustomerStaffFilter(django_filters.FilterSet):
    """Filter for staff customer management"""
    
    # Text search
    search = django_filters.CharFilter(method='filter_search', label='Search')
    
    # Customer segment
    segment = django_filters.ChoiceFilter(
        method='filter_segment',
        choices=[
            ('VIP', 'VIP'),
            ('LOYAL', 'Loyal'),
            ('REPEAT', 'Repeat'),
            ('NEW', 'New'),
            ('PROSPECT', 'Prospect')
        ],
        label='Customer Segment'
    )
    
    # Activity filters
    is_active = django_filters.BooleanFilter(field_name='user__is_active', label='Is Active')
    has_orders = django_filters.BooleanFilter(method='filter_has_orders', label='Has Orders')
    has_wishlist = django_filters.BooleanFilter(method='filter_has_wishlist', label='Has Wishlist Items')
    
    # Date filters
    date_joined_after = django_filters.DateFilter(field_name='user__date_joined', lookup_expr='gte', label='Joined After')
    date_joined_before = django_filters.DateFilter(field_name='user__date_joined', lookup_expr='lte', label='Joined Before')
    last_login_after = django_filters.DateFilter(field_name='user__last_login', lookup_expr='gte', label='Last Login After')
    last_login_before = django_filters.DateFilter(field_name='user__last_login', lookup_expr='lte', label='Last Login Before')
    
    # Spending filters
    min_spent = django_filters.NumberFilter(method='filter_min_spent', label='Minimum Total Spent')
    max_spent = django_filters.NumberFilter(method='filter_max_spent', label='Maximum Total Spent')
    
    # Order count filters
    min_orders = django_filters.NumberFilter(method='filter_min_orders', label='Minimum Orders')
    max_orders = django_filters.NumberFilter(method='filter_max_orders', label='Maximum Orders')
    
    # Location filters
    city = django_filters.CharFilter(method='filter_city', label='City')
    postal_code = django_filters.CharFilter(method='filter_postal_code', label='Postal Code')
    
    class Meta:
        model = CustomerProxy
        fields = []
    
    def filter_search(self, queryset, name, value):
        """Search across customer name, email, and phone"""
        if not value:
            return queryset
        
        return queryset.filter(
            Q(first_name__icontains=value) |
            Q(last_name__icontains=value) |
            Q(user__email__icontains=value) |
            Q(user__phone_number__icontains=value)
        )
    
    def filter_segment(self, queryset, name, value):
        """Filter by customer segment"""
        if not value:
            return queryset
        
        # This is a complex filter that would need to be implemented
        # based on the business logic for customer segments
        # For now, return the queryset as-is
        return queryset
    
    def filter_has_orders(self, queryset, name, value):
        """Filter customers who have/don't have orders"""
        if value is True:
            return queryset.filter(order__isnull=False).distinct()
        elif value is False:
            return queryset.filter(order__isnull=True)
        return queryset
    
    def filter_has_wishlist(self, queryset, name, value):
        """Filter customers who have/don't have wishlist items"""
        if value is True:
            return queryset.filter(wishlists__isnull=False).distinct()
        elif value is False:
            return queryset.filter(wishlists__isnull=True)
        return queryset
    
    def filter_min_spent(self, queryset, name, value):
        """Filter customers with minimum total spending"""
        if value is None:
            return queryset
        
        return queryset.annotate(
            total_spent=Sum('order__total', filter=Q(order__payment_status='Paid'))
        ).filter(total_spent__gte=value)
    
    def filter_max_spent(self, queryset, name, value):
        """Filter customers with maximum total spending"""
        if value is None:
            return queryset
        
        return queryset.annotate(
            total_spent=Sum('order__total', filter=Q(order__payment_status='Paid'))
        ).filter(total_spent__lte=value)
    
    def filter_min_orders(self, queryset, name, value):
        """Filter customers with minimum order count"""
        if value is None:
            return queryset
        
        return queryset.annotate(
            orders_count=Count('order', filter=Q(order__payment_status='Paid'))
        ).filter(orders_count__gte=value)
    
    def filter_max_orders(self, queryset, name, value):
        """Filter customers with maximum order count"""
        if value is None:
            return queryset
        
        return queryset.annotate(
            orders_count=Count('order', filter=Q(order__payment_status='Paid'))
        ).filter(orders_count__lte=value)
    
    def filter_city(self, queryset, name, value):
        """Filter customers by city"""
        if not value:
            return queryset
        
        return queryset.filter(
            address__city_or_village__icontains=value
        ).distinct()
    
    def filter_postal_code(self, queryset, name, value):
        """Filter customers by postal code"""
        if not value:
            return queryset
        
        return queryset.filter(
            address__postal_code__icontains=value
        ).distinct()


class AddressStaffFilter(django_filters.FilterSet):
    """Filter for staff address management"""
    
    # Text search
    search = django_filters.CharFilter(method='filter_search', label='Search')
    
    # Customer filters
    customer_name = django_filters.CharFilter(method='filter_customer_name', label='Customer Name')
    customer_email = django_filters.CharFilter(method='filter_customer_email', label='Customer Email')
    
    # Location filters
    city = django_filters.CharFilter(field_name='city_or_village', lookup_expr='icontains', label='City')
    postal_code = django_filters.CharFilter(field_name='postal_code', lookup_expr='icontains', label='Postal Code')
    
    # Usage filters
    has_orders = django_filters.BooleanFilter(method='filter_has_orders', label='Used for Orders')
    
    class Meta:
        model = AddressProxy
        fields = []
    
    def filter_search(self, queryset, name, value):
        """Search across address fields"""
        if not value:
            return queryset
        
        return queryset.filter(
            Q(full_name__icontains=value) |
            Q(street_name__icontains=value) |
            Q(address_line_1__icontains=value) |
            Q(address_line_2__icontains=value) |
            Q(city_or_village__icontains=value) |
            Q(postal_code__icontains=value)
        )
    
    def filter_customer_name(self, queryset, name, value):
        """Filter by customer name"""
        if not value:
            return queryset
        
        return queryset.filter(
            Q(customer__first_name__icontains=value) |
            Q(customer__last_name__icontains=value)
        )
    
    def filter_customer_email(self, queryset, name, value):
        """Filter by customer email"""
        if not value:
            return queryset
        
        return queryset.filter(
            customer__user__email__icontains=value
        )
    
    def filter_has_orders(self, queryset, name, value):
        """Filter addresses used for orders"""
        if value is True:
            return queryset.filter(order__isnull=False).distinct()
        elif value is False:
            return queryset.filter(order__isnull=True)
        return queryset
