from rest_framework.permissions import BasePermission


class IsCustomerOwner(BasePermission):
    """
    Permission class that allows only customers to access their own orders.
    This is the primary permission for customer-focused order operations.
    """
    
    def has_permission(self, request, view):
        """Check if user is authenticated and has a customer profile"""
        return (
            request.user.is_authenticated and 
            hasattr(request.user, 'customer')
        )
    
    def has_object_permission(self, request, view, obj):
        """Check if the order belongs to the authenticated customer"""
        return obj.customer == request.user.customer


class IsCustomerOrStaff(BasePermission):
    """
    Permission class that allows customers to access their own orders
    and staff to access all orders. Used for read-only operations.
    """
    
    def has_permission(self, request, view):
        """Allow authenticated users"""
        return request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        """Allow staff to access all orders, customers only their own"""
        if request.user.is_staff:
            return True
        
        return (
            hasattr(request.user, 'customer') and 
            obj.customer == request.user.customer
        )
