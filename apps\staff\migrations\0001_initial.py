# Generated by Django 5.2.4 on 2025-07-24 13:19

import django.contrib.auth.models
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('cart', '0002_initial'),
        ('customers', '0001_initial'),
        ('order', '0002_initial'),
        ('payments', '0001_initial'),
        ('products', '0001_initial'),
        ('wishlist', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AddressProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Address',
                'verbose_name_plural': 'Staff Addresses',
                'permissions': [('view_all_addresses', 'Can view all customer addresses'), ('manage_customer_addresses', 'Can manage customer addresses')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('customers.address',),
        ),
        migrations.CreateModel(
            name='AttributeProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Attribute',
                'verbose_name_plural': 'Staff Attributes',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.attribute',),
        ),
        migrations.CreateModel(
            name='AttributeValueProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Attribute Value',
                'verbose_name_plural': 'Staff Attribute Values',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.attributevalue',),
        ),
        migrations.CreateModel(
            name='BrandProductTypeProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Brand Product Type',
                'verbose_name_plural': 'Staff Brand Product Types',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.brandproducttype',),
        ),
        migrations.CreateModel(
            name='BrandProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Brand',
                'verbose_name_plural': 'Staff Brands',
                'permissions': [('manage_brand_product_types', 'Can manage brand product types')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.brand',),
        ),
        migrations.CreateModel(
            name='CartItemProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Cart Item',
                'verbose_name_plural': 'Staff Cart Items',
                'permissions': [('view_all_cart_items', 'Can view all cart items'), ('modify_cart_items', 'Can modify customer cart items')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('cart.cartitem',),
        ),
        migrations.CreateModel(
            name='CartProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Cart',
                'verbose_name_plural': 'Staff Carts',
                'permissions': [('view_all_carts', 'Can view all customer carts'), ('manage_abandoned_carts', 'Can manage abandoned carts'), ('bulk_cart_operations', 'Can perform bulk cart operations'), ('cart_analytics', 'Can view cart analytics')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('cart.cart',),
        ),
        migrations.CreateModel(
            name='CategoryProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Category',
                'verbose_name_plural': 'Staff Categories',
                'permissions': [('move_category', 'Can move category in tree'), ('bulk_update_categories', 'Can bulk update categories')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.category',),
        ),
        migrations.CreateModel(
            name='CustomerProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Customer',
                'verbose_name_plural': 'Staff Customers',
                'permissions': [('view_all_customers', 'Can view all customers'), ('manage_customer_data', 'Can manage customer data'), ('customer_analytics', 'Can view customer analytics'), ('bulk_customer_operations', 'Can perform bulk customer operations'), ('customer_support_access', 'Can access customer support features')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('customers.customer',),
        ),
        migrations.CreateModel(
            name='DiscountProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Discount',
                'verbose_name_plural': 'Staff Discounts',
                'permissions': [('apply_discount', 'Can apply discounts to variants')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.discount',),
        ),
        migrations.CreateModel(
            name='OrderProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Order',
                'verbose_name_plural': 'Staff Orders',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('order.order',),
        ),
        migrations.CreateModel(
            name='PaymentOptionProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Payment Option',
                'verbose_name_plural': 'Staff Payment Options',
                'permissions': [('manage_payment_options', 'Can manage payment options'), ('payment_analytics', 'Can view payment analytics'), ('payment_monitoring', 'Can monitor payment transactions')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('payments.paymentoption',),
        ),
        migrations.CreateModel(
            name='PayPalOrderProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff PayPal Order',
                'verbose_name_plural': 'Staff PayPal Orders',
                'permissions': [('view_paypal_transactions', 'Can view PayPal transactions'), ('manage_paypal_disputes', 'Can manage PayPal disputes')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('payments.paypalorder',),
        ),
        migrations.CreateModel(
            name='ProductAttributeValueProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product Attribute Value',
                'verbose_name_plural': 'Staff Product Attribute Values',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.productattributevalue',),
        ),
        migrations.CreateModel(
            name='ProductImageProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product Image',
                'verbose_name_plural': 'Staff Product Images',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.productimage',),
        ),
        migrations.CreateModel(
            name='ProductProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product',
                'verbose_name_plural': 'Staff Products',
                'permissions': [('bulk_update_products', 'Can bulk update products'), ('change_product_status', 'Can change product status'), ('manage_product_variants', 'Can manage product variants'), ('manage_product_images', 'Can manage product images')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.product',),
        ),
        migrations.CreateModel(
            name='ProductTypeAttributeProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product Type Attribute',
                'verbose_name_plural': 'Staff Product Type Attributes',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.producttypeattribute',),
        ),
        migrations.CreateModel(
            name='ProductTypeProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product Type',
                'verbose_name_plural': 'Staff Product Types',
                'permissions': [('manage_type_attributes', 'Can manage product type attributes')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.producttype',),
        ),
        migrations.CreateModel(
            name='ProductVariantAttributeValueProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product Variant Attribute Value',
                'verbose_name_plural': 'Staff Product Variant Attribute Values',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.productvariantattributevalue',),
        ),
        migrations.CreateModel(
            name='ProductVariantProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product Variant',
                'verbose_name_plural': 'Staff Product Variants',
                'permissions': [('manage_variant_stock', 'Can manage variant stock'), ('manage_variant_ordering', 'Can manage variant ordering')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.productvariant',),
        ),
        migrations.CreateModel(
            name='ReviewProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Review',
                'verbose_name_plural': 'Staff Reviews',
                'permissions': [('moderate_review', 'Can moderate reviews')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.review',),
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
            ],
            options={
                'verbose_name': 'Role',
                'verbose_name_plural': 'Roles',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('auth.group',),
            managers=[
                ('objects', django.contrib.auth.models.GroupManager()),
            ],
        ),
        migrations.CreateModel(
            name='WishlistProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Wishlist',
                'verbose_name_plural': 'Staff Wishlists',
                'permissions': [('view_all_wishlists', 'Can view all customer wishlists'), ('wishlist_analytics', 'Can view wishlist analytics'), ('customer_behavior_analysis', 'Can analyze customer behavior'), ('marketing_insights', 'Can access marketing insights'), ('bulk_wishlist_operations', 'Can perform bulk wishlist operations')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('wishlist.wishlist',),
        ),
        migrations.CreateModel(
            name='BulkOrderOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_id', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('operation_type', models.CharField(choices=[('BULK_STATUS_UPDATE', 'Bulk Status Update'), ('BULK_ASSIGNMENT', 'Bulk Assignment'), ('BULK_NOTE_ADD', 'Bulk Note Addition'), ('BULK_LABEL_PRINT', 'Bulk Label Printing'), ('BULK_INVOICE_GENERATE', 'Bulk Invoice Generation'), ('BULK_WAREHOUSE_DOC', 'Bulk Warehouse Documentation')], max_length=30)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled')], default='PENDING', max_length=20)),
                ('total_items', models.PositiveIntegerField(default=0)),
                ('processed_items', models.PositiveIntegerField(default=0)),
                ('failed_items', models.PositiveIntegerField(default=0)),
                ('operation_data', models.JSONField(default=dict, help_text='Input data for the operation')),
                ('results', models.JSONField(default=dict, help_text='Results and output data')),
                ('error_message', models.TextField(blank=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('staff_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bulk_order_operations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Bulk Order Operation',
                'verbose_name_plural': 'Bulk Order Operations',
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='StaffProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(editable=False, help_text='System-generated unique employee ID', max_length=20, unique=True)),
                ('department', models.CharField(choices=[('PRODUCT', 'Product Management'), ('ORDER', 'Order Management'), ('CUSTOMER', 'Customer Management'), ('CONTENT', 'Content Management'), ('FINANCE', 'Finance & Analytics'), ('ADMIN', 'Administration'), ('IT', 'Information Technology')], help_text='Primary department', max_length=20)),
                ('position_title', models.CharField(help_text='Official job title', max_length=100)),
                ('hire_date', models.DateField(help_text='Date of hire')),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('ON_LEAVE', 'On Leave'), ('TERMINATED', 'Terminated')], default='ACTIVE', max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Administrative notes about the staff member')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('manager', models.ForeignKey(blank=True, help_text='Direct manager/supervisor', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='direct_reports', to='staff.staffprofile')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='staff_profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='OrderStatusHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('previous_status', models.CharField(max_length=20)),
                ('new_status', models.CharField(max_length=20)),
                ('changed_at', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True, help_text='Optional notes about the status change')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_history', to='order.order')),
                ('changed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='order_status_changes', to='staff.staffprofile')),
            ],
            options={
                'verbose_name': 'Order Status History',
                'verbose_name_plural': 'Order Status Histories',
                'ordering': ['-changed_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.TextField(help_text='Internal staff note about the order')),
                ('is_internal', models.BooleanField(default=True, help_text='If False, note may be visible to customer')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staff_notes', to='order.order')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='order_notes', to='staff.staffprofile')),
            ],
            options={
                'verbose_name': 'Order Note',
                'verbose_name_plural': 'Order Notes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('SHIPPING_LABEL', 'Shipping Label'), ('CUSTOMER_INVOICE', 'Customer Invoice'), ('WAREHOUSE_PICKUP', 'Warehouse Pickup Document'), ('PACKING_SLIP', 'Packing Slip'), ('DELIVERY_NOTE', 'Delivery Note')], max_length=20)),
                ('document_data', models.JSONField(default=dict, help_text='Document content and metadata')),
                ('file_path', models.CharField(blank=True, help_text='Path to generated file', max_length=500)),
                ('generated_at', models.DateTimeField(auto_now_add=True)),
                ('is_printed', models.BooleanField(default=False)),
                ('printed_at', models.DateTimeField(blank=True, null=True)),
                ('bulk_operation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_documents', to='staff.bulkorderoperation')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='order.order')),
                ('generated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_documents', to='staff.staffprofile')),
            ],
            options={
                'verbose_name': 'Order Document',
                'verbose_name_plural': 'Order Documents',
                'ordering': ['-generated_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assigned_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True, help_text='Assignment notes or instructions')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignments', to='order.order')),
                ('assigned_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='orders_assigned_by_me', to='staff.staffprofile')),
                ('assigned_to', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assigned_orders', to='staff.staffprofile')),
            ],
            options={
                'verbose_name': 'Order Assignment',
                'verbose_name_plural': 'Order Assignments',
                'ordering': ['-assigned_at'],
            },
        ),
        migrations.CreateModel(
            name='APIAccessLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('endpoint', models.CharField(db_index=True, max_length=255)),
                ('method', models.CharField(max_length=10)),
                ('status_code', models.IntegerField()),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('response_time', models.FloatField(help_text='Response time in seconds')),
                ('timestamp', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'API Access Log',
                'verbose_name_plural': 'API Access Logs',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['endpoint', 'timestamp'], name='staff_apiac_endpoin_54916c_idx'), models.Index(fields=['user', 'timestamp'], name='staff_apiac_user_id_4672c9_idx'), models.Index(fields=['status_code', 'timestamp'], name='staff_apiac_status__7d8001_idx')],
            },
        ),
        migrations.CreateModel(
            name='BulkProductOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_id', models.UUIDField(editable=False, unique=True)),
                ('operation_type', models.CharField(choices=[('BULK_CREATE', 'Bulk Create'), ('BULK_UPDATE', 'Bulk Update'), ('BULK_DELETE', 'Bulk Delete'), ('BULK_STATUS_CHANGE', 'Bulk Status Change'), ('BULK_CATEGORY_ASSIGN', 'Bulk Category Assignment'), ('BULK_ATTRIBUTE_ASSIGN', 'Bulk Attribute Assignment')], max_length=30)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled')], default='PENDING', max_length=20)),
                ('total_items', models.PositiveIntegerField()),
                ('processed_items', models.PositiveIntegerField(default=0)),
                ('failed_items', models.PositiveIntegerField(default=0)),
                ('operation_data', models.JSONField(help_text='Operation parameters and data')),
                ('results', models.JSONField(default=dict, help_text='Operation results and errors')),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('staff_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bulk_operations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-started_at'],
                'indexes': [models.Index(fields=['staff_user', '-started_at'], name='staff_bulkp_staff_u_d4bb3f_idx'), models.Index(fields=['status', '-started_at'], name='staff_bulkp_status_48ddda_idx'), models.Index(fields=['operation_type', '-started_at'], name='staff_bulkp_operati_9c046a_idx')],
            },
        ),
        migrations.CreateModel(
            name='GroupMembership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assigned_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True, help_text='Optional notes about this group assignment')),
                ('assigned_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_staff_memberships', to=settings.AUTH_USER_MODEL)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staff_memberships', to='auth.group')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staff_group_memberships', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Group Membership',
                'verbose_name_plural': 'Group Memberships',
                'ordering': ['-assigned_at'],
                'unique_together': {('user', 'group')},
            },
        ),
        migrations.CreateModel(
            name='PaymentDispute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dispute_id', models.CharField(max_length=255, unique=True)),
                ('dispute_type', models.CharField(choices=[('CHARGEBACK', 'Chargeback'), ('INQUIRY', 'Inquiry'), ('CLAIM', 'Claim'), ('REFUND_REQUEST', 'Refund Request')], max_length=20)),
                ('status', models.CharField(choices=[('OPEN', 'Open'), ('UNDER_REVIEW', 'Under Review'), ('RESOLVED', 'Resolved'), ('CLOSED', 'Closed')], default='OPEN', max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('reason', models.TextField()),
                ('gateway_data', models.JSONField(default=dict)),
                ('resolution_notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_disputes', to=settings.AUTH_USER_MODEL)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_disputes', to='order.order')),
            ],
            options={
                'verbose_name': 'Payment Dispute',
                'verbose_name_plural': 'Payment Disputes',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['dispute_type'], name='staff_payme_dispute_8466fb_idx'), models.Index(fields=['status'], name='staff_payme_status_8c3159_idx'), models.Index(fields=['created_at'], name='staff_payme_created_85a848_idx')],
            },
        ),
        migrations.CreateModel(
            name='PaymentTransactionAudit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_id', models.CharField(db_index=True, max_length=255)),
                ('payment_method', models.CharField(max_length=50)),
                ('order_id', models.IntegerField()),
                ('customer_id', models.IntegerField(blank=True, null=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(max_length=50)),
                ('gateway_response', models.JSONField(default=dict)),
                ('action', models.CharField(max_length=100)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('staff_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payment_audits', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Payment Transaction Audit',
                'verbose_name_plural': 'Payment Transaction Audits',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['transaction_id'], name='staff_payme_transac_2394f6_idx'), models.Index(fields=['payment_method'], name='staff_payme_payment_29766f_idx'), models.Index(fields=['status'], name='staff_payme_status_1eb4a4_idx'), models.Index(fields=['created_at'], name='staff_payme_created_a50c94_idx')],
            },
        ),
        migrations.CreateModel(
            name='PermissionAudit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('group_created', 'Group Created'), ('group_updated', 'Group Updated'), ('group_deleted', 'Group Deleted'), ('role_created', 'Role Created'), ('role_updated', 'Role Updated'), ('role_deleted', 'Role Deleted'), ('user_added_to_group', 'User Added to Group'), ('user_removed_from_group', 'User Removed from Group'), ('bulk_users_assigned', 'Bulk Users Assigned'), ('permission_granted', 'Permission Granted'), ('permission_revoked', 'Permission Revoked'), ('permission_added_to_role', 'Permission Added to Role'), ('permission_removed_from_role', 'Permission Removed from Role'), ('staff_user_created', 'Staff User Created'), ('staff_profile_created', 'Staff Profile Created'), ('staff_profile_updated', 'Staff Profile Updated'), ('staff_status_changed', 'Staff Status Changed'), ('user_staff_toggled', 'User Staff Status Toggled'), ('unauthorized_access_attempt', 'Unauthorized Access Attempt'), ('permission_check_failed', 'Permission Check Failed'), ('suspicious_activity', 'Suspicious Activity')], db_index=True, max_length=50)),
                ('details', models.JSONField(default=dict, help_text='Additional details about the action')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('performed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('target_group', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='auth.group')),
                ('target_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='staff_permission_audits', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Permission Audit',
                'verbose_name_plural': 'Permission Audits',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['action', 'timestamp'], name='staff_permi_action_cfee2f_idx'), models.Index(fields=['performed_by', 'timestamp'], name='staff_permi_perform_4af82b_idx')],
            },
        ),
        migrations.CreateModel(
            name='ProductAudit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('CREATE', 'Created'), ('UPDATE', 'Updated'), ('DELETE', 'Deleted'), ('BULK_UPDATE', 'Bulk Updated'), ('STATUS_CHANGE', 'Status Changed'), ('VARIANT_ADD', 'Variant Added'), ('VARIANT_UPDATE', 'Variant Updated'), ('VARIANT_DELETE', 'Variant Deleted'), ('IMAGE_ADD', 'Image Added'), ('IMAGE_UPDATE', 'Image Updated'), ('IMAGE_DELETE', 'Image Deleted')], max_length=20)),
                ('changes', models.JSONField(default=dict, help_text='JSON of field changes')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='audit_logs', to='products.product')),
                ('staff_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_audit_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['product', '-timestamp'], name='staff_produ_product_b9340e_idx'), models.Index(fields=['staff_user', '-timestamp'], name='staff_produ_staff_u_9742c3_idx'), models.Index(fields=['action', '-timestamp'], name='staff_produ_action_6911d8_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='staffprofile',
            index=models.Index(fields=['department', 'status'], name='staff_staff_departm_d4ebfa_idx'),
        ),
        migrations.AddIndex(
            model_name='staffprofile',
            index=models.Index(fields=['manager', 'status'], name='staff_staff_manager_5798d6_idx'),
        ),
        migrations.AddIndex(
            model_name='staffprofile',
            index=models.Index(fields=['employee_id'], name='staff_staff_employe_e43664_idx'),
        ),
    ]
