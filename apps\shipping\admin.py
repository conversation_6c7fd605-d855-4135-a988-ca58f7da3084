from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import Box, Carrier, CarrierService, PackingRule


@admin.register(Box)
class BoxAdmin(admin.ModelAdmin):
    """Admin interface for Box model"""

    list_display = [
        'title', 'dimensions_display', 'volume_display', 'max_weight_display',
        'cost', 'is_mailer', 'is_active', 'priority', 'efficiency_ratio_display'
    ]
    list_filter = ['is_mailer', 'is_active', 'created_at']
    search_fields = ['title']
    ordering = ['priority', 'volume', 'cost']
    readonly_fields = ['volume', 'efficiency_ratio_display', 'created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'priority', 'is_active', 'is_mailer')
        }),
        ('Dimensions (cm)', {
            'fields': ('internal_length', 'internal_width', 'internal_height', 'volume')
        }),
        ('Capacity & Cost', {
            'fields': ('max_weight', 'cost', 'efficiency_ratio_display')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def dimensions_display(self, obj):
        """Display dimensions in a readable format"""
        return f"{obj.internal_length} × {obj.internal_width} × {obj.internal_height} cm"
    dimensions_display.short_description = "Dimensions (L×W×H)"

    def volume_display(self, obj):
        """Display volume with units"""
        return f"{obj.volume:,.0f} cm³"
    volume_display.short_description = "Volume"

    def max_weight_display(self, obj):
        """Display max weight with units"""
        return f"{obj.max_weight:,.0f} g"
    max_weight_display.short_description = "Max Weight"

    def efficiency_ratio_display(self, obj):
        """Display efficiency ratio"""
        ratio = obj.get_efficiency_ratio()
        if ratio == float('inf'):
            return "N/A"
        return f"${ratio:.4f}/cm³"
    efficiency_ratio_display.short_description = "Cost Efficiency"

    actions = ['activate_boxes', 'deactivate_boxes']

    def activate_boxes(self, request, queryset):
        """Activate selected boxes"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f"{updated} boxes activated.")
    activate_boxes.short_description = "Activate selected boxes"

    def deactivate_boxes(self, request, queryset):
        """Deactivate selected boxes"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f"{updated} boxes deactivated.")
    deactivate_boxes.short_description = "Deactivate selected boxes"


class CarrierServiceInline(admin.TabularInline):
    """Inline admin for CarrierService"""

    model = CarrierService
    extra = 1
    fields = [
        'service_name', 'service_code', 'is_active', 'estimated_days',
        'max_days', 'cost_multiplier', 'supports_insurance', 'supports_signature'
    ]


@admin.register(Carrier)
class CarrierAdmin(admin.ModelAdmin):
    """Admin interface for Carrier model"""

    list_display = [
        'title', 'code', 'is_active', 'priority', 'base_cost',
        'max_weight_display', 'service_count', 'supports_tracking'
    ]
    list_filter = ['is_active', 'supports_tracking', 'created_at']
    search_fields = ['title', 'code']
    ordering = ['-priority', 'title']
    readonly_fields = ['service_count', 'created_at', 'updated_at']
    inlines = [CarrierServiceInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'code', 'priority', 'is_active')
        }),
        ('API Configuration', {
            'fields': ('api_endpoint', 'api_key', 'api_secret'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('base_cost', 'max_weight', 'supports_tracking')
        }),
        ('Statistics', {
            'fields': ('service_count',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def max_weight_display(self, obj):
        """Display max weight with units"""
        return f"{obj.max_weight:,.0f} g"
    max_weight_display.short_description = "Max Weight"

    def service_count(self, obj):
        """Display count of active services"""
        active_count = obj.services.filter(is_active=True).count()
        total_count = obj.services.count()
        return f"{active_count}/{total_count}"
    service_count.short_description = "Active Services"

    actions = ['activate_carriers', 'deactivate_carriers', 'test_connections']

    def activate_carriers(self, request, queryset):
        """Activate selected carriers"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f"{updated} carriers activated.")
    activate_carriers.short_description = "Activate selected carriers"

    def deactivate_carriers(self, request, queryset):
        """Deactivate selected carriers"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f"{updated} carriers deactivated.")
    deactivate_carriers.short_description = "Deactivate selected carriers"

    def test_connections(self, request, queryset):
        """Test connections for selected carriers"""
        from .services.carriers import get_carrier_instance

        results = []
        for carrier in queryset:
            try:
                carrier_instance = get_carrier_instance(carrier)
                connection_result = carrier_instance.test_connection()
                status = "✓ Connected" if connection_result else "✗ Failed"
                results.append(f"{carrier.title}: {status}")
            except Exception as e:
                results.append(f"{carrier.title}: ✗ Error - {str(e)}")

        message = "Connection test results:\n" + "\n".join(results)
        self.message_user(request, message)
    test_connections.short_description = "Test carrier connections"


@admin.register(PackingRule)
class PackingRuleAdmin(admin.ModelAdmin):
    """Admin interface for PackingRule model"""

    list_display = [
        'title', 'priority', 'is_active', 'conditions_summary',
        'preferred_box', 'action_summary_display'
    ]
    list_filter = [
        'is_active', 'force_mailer', 'force_separate_packaging',
        'preferred_box', 'created_at'
    ]
    search_fields = ['title', 'description']
    ordering = ['-priority', 'title']
    readonly_fields = ['action_summary_display', 'created_at', 'updated_at']
    filter_horizontal = ['product_types']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'priority', 'is_active')
        }),
        ('Conditions', {
            'fields': (
                ('min_weight', 'max_weight'),
                ('min_volume', 'max_volume'),
                ('min_items', 'max_items'),
                'product_types'
            )
        }),
        ('Actions', {
            'fields': (
                'preferred_box',
                ('force_mailer', 'force_separate_packaging'),
                ('additional_cost', 'cost_multiplier')
            )
        }),
        ('Summary', {
            'fields': ('action_summary_display',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def conditions_summary(self, obj):
        """Display summary of rule conditions"""
        conditions = []

        if obj.min_weight or obj.max_weight:
            weight_range = []
            if obj.min_weight:
                weight_range.append(f"≥{obj.min_weight}g")
            if obj.max_weight:
                weight_range.append(f"≤{obj.max_weight}g")
            conditions.append(f"Weight: {' & '.join(weight_range)}")

        if obj.min_volume or obj.max_volume:
            volume_range = []
            if obj.min_volume:
                volume_range.append(f"≥{obj.min_volume}cm³")
            if obj.max_volume:
                volume_range.append(f"≤{obj.max_volume}cm³")
            conditions.append(f"Volume: {' & '.join(volume_range)}")

        if obj.min_items or obj.max_items:
            item_range = []
            if obj.min_items:
                item_range.append(f"≥{obj.min_items}")
            if obj.max_items:
                item_range.append(f"≤{obj.max_items}")
            conditions.append(f"Items: {' & '.join(item_range)}")

        if obj.product_types.exists():
            type_count = obj.product_types.count()
            conditions.append(f"Product types: {type_count} selected")

        return "; ".join(conditions) if conditions else "No conditions"
    conditions_summary.short_description = "Conditions"

    def action_summary_display(self, obj):
        """Display action summary with formatting"""
        summary = obj.get_action_summary()
        return format_html('<span style="font-family: monospace;">{}</span>', summary)
    action_summary_display.short_description = "Actions Summary"

    actions = ['activate_rules', 'deactivate_rules', 'test_rules']

    def activate_rules(self, request, queryset):
        """Activate selected rules"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f"{updated} rules activated.")
    activate_rules.short_description = "Activate selected rules"

    def deactivate_rules(self, request, queryset):
        """Deactivate selected rules"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f"{updated} rules deactivated.")
    deactivate_rules.short_description = "Deactivate selected rules"

    def test_rules(self, request, queryset):
        """Test selected rules with sample data"""
        # This would open a custom admin page for testing rules
        # For now, just show a message
        self.message_user(
            request,
            f"Rule testing feature available via API endpoint: /api/shipping/rules/{{id}}/test_rule/"
        )
    test_rules.short_description = "Test selected rules"


# Custom admin site configuration
admin.site.site_header = "Shipping Management"
admin.site.site_title = "Shipping Admin"
admin.site.index_title = "Shipping Configuration"
