from rest_framework import serializers
from django.db.models import Count, Sum, Avg
from django.utils import timezone
from datetime import timedelta
from .models import CustomerProxy, AddressProxy
from apps.core.serializers import ConsolidatedUserSerializer


class CustomerStaffSerializer(serializers.ModelSerializer):
    """Serializer for staff customer management"""
    user = ConsolidatedUserSerializer(read_only=True)
    full_name = serializers.SerializerMethodField()
    orders_count = serializers.SerializerMethodField()
    total_spent = serializers.SerializerMethodField()
    wishlist_count = serializers.SerializerMethodField()
    addresses_count = serializers.SerializerMethodField()
    customer_segment = serializers.SerializerMethodField()
    last_order_date = serializers.SerializerMethodField()
    
    class Meta:
        model = CustomerProxy
        fields = [
            'id', 'user', 'first_name', 'last_name', 'full_name',
            'birth_date', 'birth_date_change_count', 'orders_count',
            'total_spent', 'wishlist_count', 'addresses_count',
            'customer_segment', 'last_order_date'
        ]
    
    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}".strip()
    
    def get_orders_count(self, obj):
        return obj.get_orders_count()
    
    def get_total_spent(self, obj):
        return obj.get_total_spent()
    
    def get_wishlist_count(self, obj):
        return obj.get_wishlist_count()
    
    def get_addresses_count(self, obj):
        return obj.address.count()
    
    def get_customer_segment(self, obj):
        return obj.get_customer_segment()
    
    def get_last_order_date(self, obj):
        last_order = obj.order_set.order_by('-placed_at').first()
        return last_order.placed_at if last_order else None


class CustomerSummarySerializer(serializers.ModelSerializer):
    """Serializer for customer summary/list view"""
    full_name = serializers.SerializerMethodField()
    email = serializers.SerializerMethodField()
    phone_number = serializers.SerializerMethodField()
    orders_count = serializers.SerializerMethodField()
    total_spent = serializers.SerializerMethodField()
    customer_segment = serializers.SerializerMethodField()
    last_login = serializers.SerializerMethodField()
    is_active = serializers.SerializerMethodField()
    
    class Meta:
        model = CustomerProxy
        fields = [
            'id', 'full_name', 'email', 'phone_number',
            'orders_count', 'total_spent', 'customer_segment',
            'last_login', 'is_active'
        ]
    
    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}".strip()
    
    def get_email(self, obj):
        return obj.user.email if obj.user else None
    
    def get_phone_number(self, obj):
        return str(obj.user.phone_number) if obj.user and obj.user.phone_number else None
    
    def get_orders_count(self, obj):
        return getattr(obj, '_orders_count', obj.order_set.count())
    
    def get_total_spent(self, obj):
        return getattr(obj, '_total_spent', obj.get_total_spent())
    
    def get_customer_segment(self, obj):
        return obj.get_customer_segment()
    
    def get_last_login(self, obj):
        return obj.user.last_login if obj.user else None
    
    def get_is_active(self, obj):
        return obj.user.is_active if obj.user else False


class AddressStaffSerializer(serializers.ModelSerializer):
    """Serializer for staff address management"""
    customer_name = serializers.SerializerMethodField()
    customer_email = serializers.SerializerMethodField()
    full_address = serializers.SerializerMethodField()
    is_primary = serializers.SerializerMethodField()
    orders_count = serializers.SerializerMethodField()
    
    class Meta:
        model = AddressProxy
        fields = [
            'id', 'customer', 'customer_name', 'customer_email',
            'full_name', 'street_name', 'address_line_1', 'address_line_2',
            'postal_code', 'city_or_village', 'full_address',
            'is_primary', 'orders_count'
        ]
    
    def get_customer_name(self, obj):
        return f"{obj.customer.first_name} {obj.customer.last_name}".strip()
    
    def get_customer_email(self, obj):
        return obj.customer.user.email if obj.customer.user else None
    
    def get_full_address(self, obj):
        return obj.get_full_address()
    
    def get_is_primary(self, obj):
        return obj.is_primary_address()
    
    def get_orders_count(self, obj):
        return obj.get_orders_count()


class CustomerAnalyticsSerializer(serializers.Serializer):
    """Serializer for customer analytics data"""
    total_customers = serializers.IntegerField()
    active_customers = serializers.IntegerField()
    new_customers_this_month = serializers.IntegerField()
    customer_segments = serializers.DictField()
    average_order_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    customer_lifetime_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    repeat_customer_rate = serializers.FloatField()
    top_customers = serializers.ListField()
    geographic_distribution = serializers.DictField()
    customer_acquisition_trends = serializers.DictField()


class CustomerActivitySerializer(serializers.Serializer):
    """Serializer for customer activity data"""
    recent_orders = serializers.ListField()
    recent_wishlist_items = serializers.ListField()
    last_login = serializers.DateTimeField(allow_null=True)
    support_history = serializers.DictField()
    browsing_patterns = serializers.DictField()


class BulkCustomerOperationSerializer(serializers.Serializer):
    """Serializer for bulk customer operations"""
    customer_ids = serializers.ListField(
        child=serializers.IntegerField(),
        allow_empty=False
    )
    operation = serializers.ChoiceField(choices=[
        ('activate', 'Activate Customers'),
        ('deactivate', 'Deactivate Customers'),
        ('export', 'Export Customer Data'),
        ('segment', 'Update Customer Segment'),
    ])
    reason = serializers.CharField(max_length=500, required=False)
    segment = serializers.ChoiceField(
        choices=[('VIP', 'VIP'), ('LOYAL', 'Loyal'), ('REPEAT', 'Repeat'), ('NEW', 'New'), ('PROSPECT', 'Prospect')],
        required=False
    )


class CustomerUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating customer information"""
    
    class Meta:
        model = CustomerProxy
        fields = ['first_name', 'last_name', 'birth_date']
    
    def validate_birth_date(self, value):
        """Validate birth date changes"""
        if self.instance and self.instance.birth_date != value:
            if self.instance.birth_date_change_count >= 2:
                raise serializers.ValidationError(
                    "Birth date can only be changed twice."
                )
        return value
    
    def update(self, instance, validated_data):
        """Update customer with birth date change tracking"""
        if 'birth_date' in validated_data and instance.birth_date != validated_data['birth_date']:
            instance.birth_date_change_count += 1
        
        return super().update(instance, validated_data)
