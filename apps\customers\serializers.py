from rest_framework.serializers import ModelSerializer
from rest_framework import serializers
from phonenumber_field.serializerfields import PhoneNumberField
from .models import Customer, Address


class SimpleCustomer(ModelSerializer):
    class Meta:
        model = Customer
        fields = ['id', 'first_name', 'last_name']


class AddressSerializer(ModelSerializer):
    class Meta:
        model = Address
        fields = [
            'id',
            'full_name',
            'street_name',
            'address_line_1',
            'address_line_2',
            'postal_code',
            'city_or_village',
            'state_or_region',
            'country',
            'country_code',
            # 'customer'
        ]

    # This method is called when the serializer is preparing to represent the data.
    # 'instance' represents the instance of the model being serialized.
    def to_representation(self, instance):
        # First, get the default representation of the data by calling the superclass method.
        data = super().to_representation(instance)

        # 'data' now contains the default representation of the serialized instance.
        # We'll modify this representation to filter out empty string values from the data.

        # Using a dictionary comprehension to create a new dictionary:
        # For each key-value pair in 'data', include the key-value pair in the new dictionary
        # only if the value is not an empty string ('' represents an empty string).
        return {key: value for key, value in data.items() if value != ''}


# This serializer is not used in any views.
class CustomerSerializer(ModelSerializer):
    user_id = serializers.IntegerField(read_only=True)
    address = AddressSerializer(many=True)
    email = serializers.EmailField(source='user.email', read_only=True)

    # first_name = serializers.CharField(source='user.first_name', read_only=True)
    # last_name = serializers.CharField(source='user.last_name', read_only=True)

    class Meta:
        model = Customer
        fields = ['id', 'first_name', 'last_name', 'email', 'address', 'user_id', 'birth_date']


class CustomerUpdateSerializer(ModelSerializer):
    # address = AddressSerializer(many=True)

    class Meta:
        model = Customer
        fields = ['first_name', 'last_name', 'birth_date']

    # def update(self, instance, validated_data):
    #     # Extract nested address data
    #     address_data = validated_data.pop('address', None)

    #     # Update customer instance
    #     instance.phone_number = validated_data.get('phone_number', instance.phone_number)
    #     instance.save()

    #     # Handle nested address updates
    #     if address_data:
    #         # Delete existing addresses
    #         instance.address.all().delete()
    #         # Create new addresses
    #         for address in address_data:
    #             Address.objects.create(customer=instance, **address)

    #     return instance


class DetailedCustomerSerializer(ModelSerializer):
    email = serializers.EmailField(source='user.email', read_only=True)
    phone_number = serializers.CharField(source='user.phone_number', read_only=True)
    address = AddressSerializer(many=True, read_only=True)

    class Meta:
        model = Customer
        fields = ['id', 'email', 'phone_number', 'first_name', 'last_name', 'address', 'birth_date']

    # def to_representation(self, instance):
    #     representation = super().to_representation(instance)
    #     user_model = settings.AUTH_USER_MODEL
    #     user = instance.use
    #     representation['user'] = {
    #         'id': user.id,
    #         'username': user.username,
    #         'email': user.email,
    #         'first_name': user.first_name,
    #         'last_name': user.last_name,
    #     }
    #     return representation


class SimpleCustomerSerializer(ModelSerializer):
    # first_name = serializers.CharField(source='user.first_name', read_only=True)
    # last_name = serializers.CharField(source='user.last_name', read_only=True)
    email = serializers.EmailField(source='user.email', read_only=True)
    phone_number = serializers.CharField(source='user.phone_number', read_only=True)

    class Meta:
        model = Customer
        fields = ['id', 'first_name', 'last_name', 'email', 'phone_number']
