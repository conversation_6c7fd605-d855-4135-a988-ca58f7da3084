from django.contrib import admin
from .models import PaymentOption, PayPalOrder


@admin.register(PaymentOption)
class PaymentOptionAdmin(admin.ModelAdmin):
    list_display = ['id', 'slug', 'name', 'is_active']
    readonly_fields = ['id']
    ordering = ['id', 'name', 'slug', 'is_active']
    prepopulated_fields = {'slug': ('name',)}


@admin.register(PayPalOrder)
class PayPalOrderAdmin(admin.ModelAdmin):
    list_display = ['id', 'order_id', 'paypal_order_id', 'status', 'created_at', 'updated_at']
    list_filter = ['status', 'created_at', 'updated_at']
    search_fields = ['paypal_order_id', 'order__id']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']
    date_hierarchy = 'created_at'
