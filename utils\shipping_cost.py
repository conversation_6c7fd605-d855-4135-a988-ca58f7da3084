# LEGACY SHIPPING COST CALCULATION - COMMENTED OUT FOR NEW SHIPPING SERVICE
# This file is replaced by apps/shipping/services/shipping.py

# def calculate_shipping_cost(weight_in_grams):
#     # Each tuple consists of (max_weight_in_grams, cost_in_currency_units).
#     weight_brackets = [
#         (250, 15), (500, 20), (1000, 25), (2000, 30), (3000, 35),
#         (4000, 40), (5000, 45), (6000, 50), (7000, 55), (8000, 60),
#         (9000, 65), (10000, 70), (15000, 80), (20000, 90)
#     ]
#
#     # Fixed packaging cost.
#     packaging = 1.5
#
#     # Loop through each weight bracket to find the applicable shipping cost.
#     for max_weight, cost in weight_brackets:
#         # If the provided weight is less than or equal to the max weight in the current bracket,
#         # return the corresponding cost plus the packaging cost.
#         if weight_in_grams <= max_weight:
#             return cost + packaging
#
#     # If the weight exceeds the highest defined bracket (i.e., more than 20000 grams),
#     # return a default shipping cost of 90 units plus the packaging cost.
#     return 90 + packaging

# Temporary fallback function for backward compatibility during migration
def calculate_shipping_cost(weight_in_grams):
    """Temporary fallback - will be removed after shipping service migration"""
    return 15.00  # Default fallback rate
