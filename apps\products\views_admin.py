from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib.admin.views.decorators import staff_member_required
from django.shortcuts import get_object_or_404
from django.views.decorators.csrf import csrf_protect
from django.utils.decorators import method_decorator
from .models import ProductImage, ProductVariantAttributeValue

@staff_member_required
@csrf_protect
@require_POST
def delete_product_image(request, image_id):
    import logging
    logger = logging.getLogger(__name__)

    # Additional permission check
    if not request.user.has_perm('products.delete_productimage'):
        user_identifier = str(request.user)
        logger.warning(f"User {user_identifier} attempted to delete image {image_id} without permission")
        return JsonResponse({
            'success': False,
            'error': 'You do not have permission to delete product images'
        }, status=403)

    try:
        # Validate image_id is a positive integer
        try:
            image_id = int(image_id)
            if image_id <= 0:
                raise ValueError("Image ID must be a positive integer")
        except (ValueError, TypeError):
            logger.error(f"Invalid image ID format: {image_id}")
            return JsonResponse({
                'success': False,
                'error': 'Invalid image ID format'
            }, status=400)

        user_identifier = str(request.user)
        logger.info(f"User {user_identifier} attempting to delete image with ID: {image_id}")

        # Check if the image exists
        try:
            image = ProductImage.objects.get(id=image_id)
        except ProductImage.DoesNotExist:
            logger.error(f"Image with ID {image_id} not found")
            return JsonResponse({
                'success': False,
                'error': f'Image with ID {image_id} not found'
            }, status=404)

        # Store the image details for the response
        image_details = {
            'id': image.id,
            'alternative_text': image.alternative_text,
            'product_variant': str(image.product_variant)
        }

        # Delete the image
        image.delete()
        logger.info(f"Successfully deleted image with ID: {image_id}")

        return JsonResponse({
            'success': True,
            'message': f'Image "{image_details["alternative_text"]}" deleted successfully.',
            'image': image_details
        })
    except Exception as e:
        logger.exception(f"Error deleting image with ID {image_id}: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@staff_member_required
@csrf_protect
@require_POST
def delete_product_variant_attribute_value(request, attribute_value_id):
    import logging
    logger = logging.getLogger(__name__)

    # Additional permission check
    if not request.user.has_perm('products.delete_productvariantattributevalue'):
        user_identifier = str(request.user)
        logger.warning(f"User {user_identifier} attempted to delete attribute value {attribute_value_id} without permission")
        return JsonResponse({
            'success': False,
            'error': 'You do not have permission to delete product variant attribute values'
        }, status=403)

    try:
        # Validate attribute_value_id is a positive integer
        try:
            attribute_value_id = int(attribute_value_id)
            if attribute_value_id <= 0:
                raise ValueError("Attribute Value ID must be a positive integer")
        except (ValueError, TypeError):
            logger.error(f"Invalid attribute value ID format: {attribute_value_id}")
            return JsonResponse({
                'success': False,
                'error': 'Invalid attribute value ID format'
            }, status=400)

        user_identifier = str(request.user)
        logger.info(f"User {user_identifier} attempting to delete attribute value with ID: {attribute_value_id}")

        # Check if the attribute value exists
        try:
            attribute_value = ProductVariantAttributeValue.objects.get(id=attribute_value_id)
        except ProductVariantAttributeValue.DoesNotExist:
            logger.error(f"Attribute value with ID {attribute_value_id} not found")
            return JsonResponse({
                'success': False,
                'error': f'Attribute value with ID {attribute_value_id} not found'
            }, status=404)

        # Store the attribute value details for the response
        attribute_value_details = {
            'id': attribute_value.id,
            'attribute_value': str(attribute_value.attribute_value),
            'product_variant': str(attribute_value.product_variant)
        }

        # Delete the attribute value
        attribute_value.delete()
        logger.info(f"Successfully deleted attribute value with ID: {attribute_value_id}")

        return JsonResponse({
            'success': True,
            'message': f'Attribute value "{attribute_value_details["attribute_value"]}" deleted successfully.',
            'attribute_value': attribute_value_details
        })
    except Exception as e:
        logger.exception(f"Error deleting attribute value with ID {attribute_value_id}: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
