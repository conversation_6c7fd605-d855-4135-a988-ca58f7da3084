from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.order.models import Order, OrderItem
from apps.staff.authorization.models import StaffProfile
from apps.staff.common.constants import STAFF_GROUPS
from .models import OrderStatusHistory, OrderAssignment, OrderNote, BulkOrderOperation, OrderDocument


class OrderService:
    """
    Business logic service for order management operations.
    Handles complex order operations with proper validation and audit trails.
    """

    @staticmethod
    def get_orders_for_user(user, filters=None):
        """
        Get orders based on user role and permissions.
        
        Args:
            user: The staff user requesting orders
            filters: Optional dictionary of filters to apply
            
        Returns:
            QuerySet of orders the user can access
        """
        base_queryset = Order.objects.select_related(
            'customer__user',
            'selected_address',
            'payment_method'
        ).prefetch_related('ordered_items')

        if user.is_superuser:
            queryset = base_queryset
        else:
            user_groups = set(user.groups.values_list('name', flat=True))

            # Order Management Executive - all orders
            if STAFF_GROUPS['ORDER_MANAGER'] in user_groups:
                queryset = base_queryset
            # Order Fulfillment Specialist - pending and processing orders
            elif STAFF_GROUPS['ORDER_FULFILLMENT'] in user_groups:
                queryset = base_queryset.filter(
                    order_status__in=['Pending', 'Processing']
                )
            # Order Management Group Member - limited access
            elif STAFF_GROUPS['ORDER_TEAM_MEMBER'] in user_groups:
                queryset = base_queryset.filter(
                    order_status__in=['Pending', 'Processing', 'Dispatched']
                )
            # Customer Service - can view orders for support
            elif STAFF_GROUPS['CUSTOMER_SERVICE'] in user_groups:
                queryset = base_queryset
            else:
                queryset = Order.objects.none()

        # Apply additional filters if provided
        if filters:
            if 'status' in filters:
                queryset = queryset.filter(order_status=filters['status'])
            if 'payment_status' in filters:
                queryset = queryset.filter(payment_status=filters['payment_status'])
            if 'customer_id' in filters:
                queryset = queryset.filter(customer_id=filters['customer_id'])
            if 'date_from' in filters:
                queryset = queryset.filter(placed_at__gte=filters['date_from'])
            if 'date_to' in filters:
                queryset = queryset.filter(placed_at__lte=filters['date_to'])

        return queryset.order_by('-placed_at')

    @staticmethod
    @transaction.atomic
    def update_order_status(order_id, new_status, user, reason=None):
        """
        Update order status with audit trail.
        
        Args:
            order_id: ID of the order to update
            new_status: New delivery status
            user: Staff user making the change
            reason: Optional reason for the status change
            
        Returns:
            Updated order instance
            
        Raises:
            ValidationError: If status transition is invalid
        """
        try:
            order = Order.objects.get(id=order_id)
        except Order.DoesNotExist:
            raise ValidationError("Order not found")

        # Validate status transition
        valid_transitions = {
            'Pending': ['Processing'],
            'Processing': ['Dispatched'],
            'Dispatched': ['Delivered'],
            'Delivered': []  # No transitions from delivered
        }

        current_status = order.order_status
        if new_status not in valid_transitions.get(current_status, []):
            raise ValidationError(
                f"Cannot change status from '{current_status}' to '{new_status}'"
            )

        # Check if user has permission to make this change
        if not OrderService._can_change_status(user, order, new_status):
            raise ValidationError("Insufficient permissions to change order status")

        # Update order status
        previous_status = order.order_status
        order.order_status = new_status
        order.save()

        # Create status history record
        OrderStatusHistory.objects.create(
            order=order,
            previous_status=previous_status,
            new_status=new_status,
            changed_by=user.staff_profile,
            notes=reason or ''
        )

        return order

    @staticmethod
    def _can_change_status(user, order, new_status):
        """Check if user can change order to the specified status"""
        if user.is_superuser:
            return True

        user_groups = set(user.groups.values_list('name', flat=True))

        # Order Management Executive can change any status
        if STAFF_GROUPS['ORDER_MANAGER'] in user_groups:
            return True

        # Order Fulfillment Specialist can only change to Processing or Dispatched
        if STAFF_GROUPS['ORDER_FULFILLMENT'] in user_groups:
            return new_status in ['Processing', 'Dispatched']

        # Order Management Group Member can only change to Processing
        if STAFF_GROUPS['ORDER_TEAM_MEMBER'] in user_groups:
            return new_status == 'Processing'

        return False

    @staticmethod
    @transaction.atomic
    def assign_order(order_id, assigned_to_user, assigned_by_user, notes=None):
        """
        Assign order to staff member.
        
        Args:
            order_id: ID of the order to assign
            assigned_to_user: Staff user to assign the order to
            assigned_by_user: Staff user making the assignment
            notes: Optional assignment notes
            
        Returns:
            OrderAssignment instance
        """
        try:
            order = Order.objects.get(id=order_id)
        except Order.DoesNotExist:
            raise ValidationError("Order not found")

        # Deactivate existing assignments
        order.assignments.filter(is_active=True).update(is_active=False)

        # Create new assignment
        assignment = OrderAssignment.objects.create(
            order=order,
            assigned_to=assigned_to_user.staff_profile,
            assigned_by=assigned_by_user.staff_profile,
            notes=notes or ''
        )

        return assignment

    @staticmethod
    @transaction.atomic
    def bulk_update_orders(order_ids, updates, user):
        """
        Perform bulk updates on multiple orders.
        
        Args:
            order_ids: List of order IDs to update
            updates: Dictionary of fields to update
            user: Staff user performing the update
            
        Returns:
            Number of orders updated
        """
        orders = Order.objects.filter(id__in=order_ids)
        updated_count = 0

        for order in orders:
            if 'order_status' in updates:
                try:
                    OrderService.update_order_status(
                        order.id,
                        updates['order_status'],
                        user,
                        updates.get('reason', 'Bulk update')
                    )
                    updated_count += 1
                except ValidationError:
                    # Skip orders that can't be updated
                    continue

        return updated_count

    @staticmethod
    def add_order_note(order_id, note_text, user, is_internal=True):
        """
        Add a note to an order.
        
        Args:
            order_id: ID of the order
            note_text: Text content of the note
            user: Staff user adding the note
            is_internal: Whether the note is internal only
            
        Returns:
            OrderNote instance
        """
        try:
            order = Order.objects.get(id=order_id)
        except Order.DoesNotExist:
            raise ValidationError("Order not found")

        note = OrderNote.objects.create(
            order=order,
            created_by=user.staff_profile,
            note=note_text,
            is_internal=is_internal
        )

        return note

    @staticmethod
    def get_order_analytics(user, date_from=None, date_to=None):
        """
        Get order analytics data for the user's accessible orders.
        
        Args:
            user: Staff user requesting analytics
            date_from: Optional start date filter
            date_to: Optional end date filter
            
        Returns:
            Dictionary with analytics data
        """
        orders = OrderService.get_orders_for_user(user)

        if date_from:
            orders = orders.filter(placed_at__gte=date_from)
        if date_to:
            orders = orders.filter(placed_at__lte=date_to)

        # Calculate basic metrics
        total_orders = orders.count()
        total_revenue = sum(order.total or 0 for order in orders)

        # Status breakdown
        status_counts = {}
        for status_choice in Order.ORDER_STATUS:
            status = status_choice[0]
            status_counts[status] = orders.filter(order_status=status).count()

        # Payment status breakdown
        payment_counts = {}
        for payment_choice in Order.PAYMENT_STATUS:
            status = payment_choice[0]
            payment_counts[status] = orders.filter(payment_status=status).count()

        return {
            'total_orders': total_orders,
            'total_revenue': total_revenue,
            'status_breakdown': status_counts,
            'payment_breakdown': payment_counts,
            'average_order_value': total_revenue / total_orders if total_orders > 0 else 0
        }

    @staticmethod
    @transaction.atomic
    def bulk_update_order_status(order_ids, new_status, user, reason=None):
        """
        Perform bulk status updates with proper tracking and validation.

        Args:
            order_ids: List of order IDs to update
            new_status: New delivery status
            user: Staff user performing the update
            reason: Optional reason for the status change

        Returns:
            BulkOrderOperation instance with results
        """
        import uuid

        # Create bulk operation record
        bulk_op = BulkOrderOperation.objects.create(
            operation_id=uuid.uuid4(),
            staff_user=user,
            operation_type='BULK_STATUS_UPDATE',
            total_items=len(order_ids),
            operation_data={
                'order_ids': order_ids,
                'new_status': new_status,
                'reason': reason or 'Bulk status update'
            }
        )

        try:
            bulk_op.status = 'IN_PROGRESS'
            bulk_op.save()

            updated_orders = []
            failed_orders = []

            for order_id in order_ids:
                try:
                    order = OrderService.update_order_status(
                        order_id, new_status, user, reason
                    )
                    updated_orders.append({
                        'order_id': order_id,
                        'previous_status': order.status_history.first().previous_status,
                        'new_status': new_status
                    })
                    bulk_op.increment_processed()

                except ValidationError as e:
                    failed_orders.append({
                        'order_id': order_id,
                        'error': str(e)
                    })
                    bulk_op.increment_failed()

            # Update operation results
            bulk_op.results = {
                'updated_orders': updated_orders,
                'failed_orders': failed_orders,
                'success_count': len(updated_orders),
                'failure_count': len(failed_orders)
            }

            if bulk_op.failed_items == 0:
                bulk_op.mark_completed()
            else:
                bulk_op.status = 'COMPLETED'
                bulk_op.completed_at = timezone.now()
                bulk_op.save()

            return bulk_op

        except Exception as e:
            bulk_op.mark_failed(str(e))
            raise ValidationError(f"Bulk status update failed: {str(e)}")

    @staticmethod
    @transaction.atomic
    def bulk_assign_orders(order_ids, assigned_to_user, assigned_by_user, notes=None):
        """
        Perform bulk order assignments.

        Args:
            order_ids: List of order IDs to assign
            assigned_to_user: Staff user to assign orders to
            assigned_by_user: Staff user making the assignments
            notes: Optional assignment notes

        Returns:
            BulkOrderOperation instance with results
        """
        import uuid

        # Create bulk operation record
        bulk_op = BulkOrderOperation.objects.create(
            operation_id=uuid.uuid4(),
            staff_user=assigned_by_user,
            operation_type='BULK_ASSIGNMENT',
            total_items=len(order_ids),
            operation_data={
                'order_ids': order_ids,
                'assigned_to_id': assigned_to_user.staff_profile.id,
                'notes': notes or ''
            }
        )

        try:
            bulk_op.status = 'IN_PROGRESS'
            bulk_op.save()

            assigned_orders = []
            failed_orders = []

            for order_id in order_ids:
                try:
                    assignment = OrderService.assign_order(
                        order_id, assigned_to_user, assigned_by_user, notes
                    )
                    assigned_orders.append({
                        'order_id': order_id,
                        'assignment_id': assignment.id,
                        'assigned_to': assigned_to_user.email
                    })
                    bulk_op.increment_processed()

                except ValidationError as e:
                    failed_orders.append({
                        'order_id': order_id,
                        'error': str(e)
                    })
                    bulk_op.increment_failed()

            # Update operation results
            bulk_op.results = {
                'assigned_orders': assigned_orders,
                'failed_orders': failed_orders,
                'success_count': len(assigned_orders),
                'failure_count': len(failed_orders)
            }

            if bulk_op.failed_items == 0:
                bulk_op.mark_completed()
            else:
                bulk_op.status = 'COMPLETED'
                bulk_op.completed_at = timezone.now()
                bulk_op.save()

            return bulk_op

        except Exception as e:
            bulk_op.mark_failed(str(e))
            raise ValidationError(f"Bulk assignment failed: {str(e)}")

    @staticmethod
    @transaction.atomic
    def bulk_generate_documents(order_ids, document_types, user, include_customer_invoice=False,
                                include_warranty_info=False):
        """
        Generate documents for multiple orders.

        Args:
            order_ids: List of order IDs
            document_types: List of document types to generate
            user: Staff user generating documents
            include_customer_invoice: Whether to include customer invoice
            include_warranty_info: Whether to include warranty information

        Returns:
            BulkOrderOperation instance with results
        """
        import uuid

        # Create bulk operation record
        bulk_op = BulkOrderOperation.objects.create(
            operation_id=uuid.uuid4(),
            staff_user=user,
            operation_type='BULK_LABEL_PRINT' if 'SHIPPING_LABEL' in document_types else 'BULK_INVOICE_GENERATE',
            total_items=len(order_ids) * len(document_types),
            operation_data={
                'order_ids': order_ids,
                'document_types': document_types,
                'include_customer_invoice': include_customer_invoice,
                'include_warranty_info': include_warranty_info
            }
        )

        try:
            bulk_op.status = 'IN_PROGRESS'
            bulk_op.save()

            generated_documents = []
            failed_documents = []

            for order_id in order_ids:
                try:
                    order = Order.objects.get(id=order_id)

                    for doc_type in document_types:
                        try:
                            document = OrderService._generate_single_document(
                                order, doc_type, user, bulk_op, include_customer_invoice, include_warranty_info
                            )
                            generated_documents.append({
                                'order_id': order_id,
                                'document_id': document.id,
                                'document_type': doc_type,
                                'file_path': document.file_path
                            })
                            bulk_op.increment_processed()

                        except Exception as e:
                            failed_documents.append({
                                'order_id': order_id,
                                'document_type': doc_type,
                                'error': str(e)
                            })
                            bulk_op.increment_failed()

                except Order.DoesNotExist:
                    for doc_type in document_types:
                        failed_documents.append({
                            'order_id': order_id,
                            'document_type': doc_type,
                            'error': 'Order not found'
                        })
                        bulk_op.increment_failed()

            # Update operation results
            bulk_op.results = {
                'generated_documents': generated_documents,
                'failed_documents': failed_documents,
                'success_count': len(generated_documents),
                'failure_count': len(failed_documents)
            }

            if bulk_op.failed_items == 0:
                bulk_op.mark_completed()
            else:
                bulk_op.status = 'COMPLETED'
                bulk_op.completed_at = timezone.now()
                bulk_op.save()

            return bulk_op

        except Exception as e:
            bulk_op.mark_failed(str(e))
            raise ValidationError(f"Bulk document generation failed: {str(e)}")

    @staticmethod
    def _generate_single_document(order, document_type, user, bulk_operation=None, include_customer_invoice=False,
                                  include_warranty_info=False):
        """
        Generate a single document for an order.

        Args:
            order: Order instance
            document_type: Type of document to generate
            user: Staff user generating the document
            bulk_operation: Optional bulk operation instance
            include_customer_invoice: Whether to include customer invoice details
            include_warranty_info: Whether to include warranty information

        Returns:
            OrderDocument instance
        """
        # Generate document content based on type
        document_data = OrderService._prepare_document_data(
            order, document_type, include_customer_invoice, include_warranty_info
        )

        # Create document record
        document = OrderDocument.objects.create(
            order=order,
            document_type=document_type,
            generated_by=user.staff_profile,
            bulk_operation=bulk_operation,
            document_data=document_data,
            file_path=f"documents/{document_type.lower()}/{order.id}_{document_type.lower()}.pdf"
        )

        return document

    @staticmethod
    def _prepare_document_data(order, document_type, include_customer_invoice=False, include_warranty_info=False):
        """
        Prepare document data based on document type.

        Args:
            order: Order instance
            document_type: Type of document
            include_customer_invoice: Whether to include customer invoice details
            include_warranty_info: Whether to include warranty information

        Returns:
            Dictionary with document data
        """
        base_data = {
            'order_id': order.id,
            'customer_name': f"{order.customer.first_name} {order.customer.last_name}",
            'customer_email': order.customer.user.email,
            'order_date': order.placed_at.isoformat(),
            'total_amount': str(order.total),
            'delivery_address': {
                'street': order.selected_address.street_address,
                'city': order.selected_address.city,
                'state': order.selected_address.state,
                'postal_code': order.selected_address.postal_code,
                'country': order.selected_address.country,
            } if order.selected_address else None,
            'items': [
                {
                    'product_name': item.product.title,
                    'variant': item.product_variant.title if item.product_variant else None,
                    'quantity': item.quantity,
                    'price': str(item.total_price),
                    'sku': item.product_variant.sku if item.product_variant else None,
                }
                for item in order.ordered_items.all()
            ]
        }

        if document_type == 'SHIPPING_LABEL':
            base_data.update({
                'tracking_number': f"TRK{order.id:08d}",
                'shipping_method': order.payment_method.name,
                'weight': order.total_weight,
                'shipping_cost': str(order.shipping_cost),
            })

        elif document_type == 'CUSTOMER_INVOICE':
            base_data.update({
                'invoice_number': f"INV{order.id:08d}",
                'payment_status': order.payment_status,
                'payment_method': order.payment_method.name,
                'subtotal': str(order.subtotal),
                'shipping_cost': str(order.shipping_cost),
                'include_warranty': include_warranty_info,
                'warranty_info': {
                    'warranty_period': '1 year',
                    'warranty_terms': 'Standard manufacturer warranty applies',
                    'support_contact': '<EMAIL>'
                } if include_warranty_info else None
            })

        elif document_type == 'WAREHOUSE_PICKUP':
            base_data.update({
                'pickup_number': f"PKP{order.id:08d}",
                'priority': 'HIGH' if order.total > 500 else 'NORMAL',
                'special_instructions': 'Handle with care' if any(
                    'fragile' in item.extra_data.get('notes', '').lower()
                    for item in order.ordered_items.all()
                ) else None,
                'warehouse_sections': [
                    f"Section {item.product.category.title[:3].upper()}"
                    for item in order.ordered_items.all()
                ]
            })

        elif document_type == 'PACKING_SLIP':
            base_data.update({
                'packing_slip_number': f"PKS{order.id:08d}",
                'packing_instructions': 'Standard packing',
                'fragile_items': [
                    item.product.title for item in order.ordered_items.all()
                    if 'fragile' in item.extra_data.get('notes', '').lower()
                ]
            })

        return base_data
