from django.urls import path, include

app_name = 'staff'

urlpatterns = [
    # Authorization domain (RBAC system)
    path('', include('apps.staff.authorization.urls')),

    # Products domain
    path('products/', include('apps.staff.products.urls')),

    # Orders domain
    path('orders/', include('apps.staff.orders.urls')),

    # Customers domain
    path('customers/', include('apps.staff.customers.urls')),

    # Payments domain
    path('payments/', include('apps.staff.payments.urls')),

    # Wishlist domain
    path('wishlist/', include('apps.staff.wishlist.urls')),

    # Cart domain
    path('cart/', include('apps.staff.cart.urls')),
]
