# Selective Checkout Architecture Overview

## Introduction

This document outlines the implementation of bulk selection and selective checkout features for the Picky Store e-commerce application. The feature allows users to select specific cart items for checkout while keeping unselected items in the cart for future purchase.

## Current vs. New Architecture

### Current Flow

1. User adds items to cart → All `CartItem` records created
2. User proceeds to checkout → ALL cart items processed
3. Order created → ALL cart items become `OrderItem` records
4. Cart completely deleted → No items remain

### New Flow

1. User adds items to cart → All `CartItem` records created with `is_selected=True`
2. User can select/deselect items → Selection state tracked per item
3. User proceeds to checkout → Only SELECTED cart items processed
4. Order created → Only SELECTED cart items become `OrderItem` records
5. Cart cleanup → Only processed items removed, unselected items remain
6. If no items remain → Cart deleted automatically

## Database Schema Changes

### CartItem Model Enhancement

```python
class CartItem(models.Model):
    # ... existing fields ...
    is_selected = models.BooleanField(default=True)
    # Note: the timestamp field `selected_at` was removed — selection is persisted as a boolean only.
```

**Migration Required**: Add the `is_selected` boolean field (default True) for existing records. No `selected_at` timestamp is stored by the current implementation.

## API Endpoints

### New Cart Selection Endpoints

- `PATCH /api/cart/{cart_id}/items/{item_id}/select/` - Toggle item selection
- `POST /api/cart/{cart_id}/items/bulk-select/` - Bulk select items
- `POST /api/cart/{cart_id}/items/bulk-deselect/` - Bulk deselect items
- `GET /api/cart/{cart_id}/selected-summary/` - Get selected items summary

### Modified Endpoints

- `GET /api/cart/{cart_id}/` - Include selection state and selected totals
- `POST /api/orders/` - Accept `selected_cart_item_ids` parameter

## Key Features

### 1. Item Selection Management

- Individual item selection/deselection
- Bulk select/deselect all items
- Selection state persistence
- Real-time total calculations for selected items

### 2. Selective Checkout Process

- Process only selected cart items during order creation
- Calculate shipping costs for selected items only
- Maintain unselected items in cart after checkout
- Automatic cart deletion when empty

### 3. Enhanced Cart Calculations

- Separate totals for selected vs. all items
- Selected items count and weight
- Shipping calculations based on selected items only

## Frontend Integration

### State Management

- Server is the source-of-truth for selection: the frontend derives selection state from the cart payload returned by the backend. The client may keep UI-only state for transient interactions, but all persistent selection updates must be saved via the selection APIs.
- Selection hooks for UI components
- Real-time calculation updates

### UI Components

- Checkboxes for individual item selection (driven by server `is_selected`)
- "Select All" functionality (sends bulk-select/bulk-deselect requests)
- Bulk action toolbar
- Updated price summary for selected items

## Technical Considerations

### Performance

- Database indexes on `is_selected` field
- Optimized queries for selected items
- Efficient bulk operations

### Data Consistency

- Atomic transactions for order processing
- Validation of selected items during checkout
- Proper error handling for edge cases

### User Experience

- Mobile-first responsive design
- Clear visual indicators for selection state
- Intuitive bulk operation controls
- Seamless integration with existing checkout flow

## Implementation Phases

1. **Phase 1**: Database changes and API endpoints
2. **Phase 2**: Order processing modifications
3. **Phase 3**: Frontend UI implementation

## Testing Strategy

- Unit tests for new API endpoints
- Integration tests for selective checkout flow
- Frontend component testing
- End-to-end checkout scenarios
- Postman collection for API testing
