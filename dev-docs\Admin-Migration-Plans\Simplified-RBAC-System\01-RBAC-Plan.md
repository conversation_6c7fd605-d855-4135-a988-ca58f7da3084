# 01: Enhanced RBAC System—Architectural Plan (v2.0)

## **System Overview**

This document outlines an enhanced Role-Based Access Control (RBAC) system built on Django's
native Groups and Permissions framework, exposed exclusively through Django REST Framework APIs.
The system features permission-based authorization (not hardcoded roles), a Role proxy model for
better abstractions, comprehensive staff management capabilities, organizational hierarchy support,
and robust circular dependency prevention while maintaining clear permission boundaries.

## **Core Principles**

### **Enhanced Simplicity with Robust Architecture**

- Leverage Django's built-in Groups and Permissions with Role proxy model
- **Permission-based authorization** instead of hardcoded role checks
- Clear organizational hierarchy with circular dependency prevention
- Direct role-to-permission mapping with enhanced abstractions
- Business-focused role names with comprehensive staff management capabilities
- Custom permissions for fine-grained access control

### **Separation of Concerns**

- **Authentication**: Use existing core app authentication endpoints
- **Authorization**: Staff app handles permission checking and group management
- React Admin frontend for user interaction
- Django Admin Panel restricted to Superusers only

### **Single Source of Truth**

- Authentication logic remains in the core app
- Staff app focuses purely on authorization and group management
- No duplication of authentication functionality
- Consistent token management across the system

### **Clear Permission Boundaries**

- Each group has specific, well-defined permissions
- No overlapping or ambiguous access rights
- Business-aligned group structures
- Explicit permission enforcement

---

## **User Types & Access Levels**

### **1. Superuser**

- **Purpose**: System administration and maintenance
- **Access**: Full Django Admin Panel + All API endpoints
- **Responsibilities**: System maintenance, user management, technical operations
- **Restrictions**: Not involved in business operations

### **2. Staff Management Roles**

- **Staff Manager (SM)**: Complete staff user lifecycle management
- **Department Head (DH)**: Department-level staff management and oversight
- **HR Administrator (HRA)**: Staff onboarding, profile management, and HR operations
- **Access**: API-only with comprehensive staff management capabilities
- **Responsibilities**: Staff creation, role assignments, organizational management

### **3. Business Function Roles**

- **Purpose**: Specific business function execution with clear hierarchies
- **Access**: API-only with role-specific permissions
- **Responsibilities**: Domain-specific operations based on role membership
- **Permissions**: Granular permissions based on role level (Executive, Team Member, Specialist)

---

## **Role Structure & Permissions**

### **Staff Management Roles**

#### **Staff Manager (SM)**

- **Business Role**: Complete staff user lifecycle management and system oversight
- **Permissions**:
  - User Management: Full CRUD operations on user accounts
  - Role Management: Create, modify, and assign roles
  - Staff Profiles: Full CRUD operations on staff profiles
  - Group Memberships: Manage all user-role assignments
  - Audit Access: View comprehensive audit logs
  - Department Oversight: Cross-department staff management

#### **Department Head (DH)**

- **Business Role**: Department-level staff management and team leadership
- **Permissions**:
  - User Management: View and modify users within department
  - Role Management: View roles and limited assignment capabilities
  - Staff Profiles: Manage direct reports and department staff
  - Team Management: Assign roles within department scope
  - Reporting: Department-level analytics and reports

#### **HR Administrator (HRA)**

- **Business Role**: Staff onboarding, profile management, and HR operations
- **Permissions**:
  - User Creation: Create new staff user accounts
  - Staff Profiles: Full CRUD operations on staff profiles
  - Onboarding: Manage staff onboarding workflows
  - HR Records: Maintain employment records and status
  - Compliance: Ensure role assignment compliance

### **Product Management Roles**

#### **Product Management Executive (PME)**

- **Business Role**: Senior product management with full authority
- **Permissions**:
  - Products: Full CRUD (Create, Read, Update, Delete)
  - Categories: Full CRUD + Reordering
  - Brands: Full CRUD
  - Product Types: Full CRUD
  - Attributes: Full CRUD
  - Product Images: Full CRUD
  - Bulk Operations: All product-related bulk actions
  - Reports: Product analytics and export

#### **Product Management Group Member (PMGM)**

- **Business Role**: Day-to-day product management operations
- **Permissions**:
  - Products: Create, Read, Update (no Delete)
  - Categories: Read, Update (no Create/Delete)
  - Brands: Read only
  - Product Types: Read only
  - Attributes: Read, Update
  - Product Images: Create, Read, Update, Delete
  - Bulk Operations: Bulk update only (no bulk delete)
  - Reports: Basic product reports

#### **Product Catalog Viewer (PCV)**

- **Business Role**: Product information access for support/sales
- **Permissions**:
  - Products: Read only
  - Categories: Read only
  - Brands: Read only
  - Product Types: Read only
  - Attributes: Read only
  - Product Images: Read only
  - Reports: View basic product information

### **Order Management Roles**

#### **Order Management Executive (OME)**

- **Business Role**: Senior order management with full authority
- **Permissions**:
  - Orders: Full CRUD + Status changes + Cancellations + Refunds
  - Order Items: Full CRUD
  - Shipping: Full management
  - Payment Status: Update capabilities
  - Customer Orders: Full access
  - Bulk Operations: All order-related bulk actions
  - Reports: Complete order analytics and export

#### **Order Management Group Member (OMGM)**

- **Business Role**: Order processing and status management
- **Permissions**:
  - Orders: Read, Update status (no Delete/Cancel)
  - Order Items: Read, Update
  - Shipping: Update shipping status
  - Payment Status: Read only
  - Customer Orders: Read access
  - Bulk Operations: Bulk status updates only
  - Reports: Basic order reports

#### **Order Fulfillment Specialist (OFS)**

- **Business Role**: Warehouse and shipping operations
- **Permissions**:
  - Orders: Read, Update shipping status only
  - Order Items: Read only
  - Shipping: Full shipping management
  - Payment Status: Read only
  - Customer Orders: Read shipping information only
  - Reports: Shipping and fulfillment reports

### **Customer Management Roles**

#### **Customer Management Executive (CME)**

- **Business Role**: Senior customer relations with full authority
- **Permissions**:
  - Customers: Full CRUD + Account management
  - Customer Addresses: Full CRUD
  - Customer Orders: Full access for support
  - Customer Reviews: Moderate, respond, delete
  - Customer Support: Full ticket management
  - Bulk Operations: All customer-related bulk actions
  - Reports: Complete customer analytics and export

#### **Customer Support Representative (CSR)**

- **Business Role**: Customer service and support operations
- **Permissions**:
  - Customers: Read, Update contact information
  - Customer Addresses: Read, Update
  - Customer Orders: Read for support purposes
  - Customer Reviews: Read, respond (no delete)
  - Customer Support: Create, update support tickets
  - Reports: Basic customer support reports

#### **Customer Data Analyst (CDA)**

- **Business Role**: Customer behavior analysis and reporting
- **Permissions**:
  - Customers: Read only (anonymized data)
  - Customer Orders: Read for analysis
  - Customer Reviews: Read only
  - Reports: Full customer analytics and export
  - Data Export: Customer behavior data

### **Content Management Roles**

#### **Content Management Executive (CTME)**

- **Business Role**: Content strategy and moderation authority
- **Permissions**:
  - Reviews: Full moderation (approve, reject, delete, respond)
  - Content: Full CRUD for site content
  - SEO: Full SEO management
  - Marketing Content: Full CRUD
  - Reports: Content performance analytics

#### **Content Moderator (CTM)**

- **Business Role**: Content review and basic moderation
- **Permissions**:
  - Reviews: Moderate, respond (no delete)
  - Content: Read, Update existing content
  - SEO: Read only
  - Marketing Content: Read, Update
  - Reports: Basic content reports

### **Finance & Analytics Roles**

#### **Finance Manager (FM)**

- **Business Role**: Financial oversight and reporting
- **Permissions**:
  - Orders: Read financial data
  - Payments: Full payment management
  - Refunds: Process refunds
  - Financial Reports: Full access
  - Revenue Analytics: Complete access
  - Data Export: Financial data export

#### **Business Analyst (BA)**

- **Business Role**: Business intelligence and reporting
- **Permissions**:
  - All Data: Read-only access for analysis
  - Reports: Full analytics across all domains
  - Data Export: Comprehensive data export capabilities
  - Dashboards: Create and manage business dashboards

---

## **Technical Architecture**

### **Data Models**

#### **Existing Django Models (Utilized)**

- **User**: Django's built-in User model (extended via Customer)
- **Group**: Django's built-in Group model
- **Permission**: Django's built-in Permission model
- **Customer**: Existing customer model linked to User

#### **New Models (Enhanced)**

- **Role**: Proxy model for Django Groups with enhanced functionality
- **StaffProfile**: Extended staff user profiles with organizational data
- **GroupMembership**: Track group assignments with metadata
- **PermissionAudit**: Comprehensive audit logging with enhanced action types
- **APIAccessLog**: Track API usage for monitoring

### **API Structure**

#### **Authentication Endpoints (Core App)**

- Login/Logout with JWT tokens (existing core endpoints)
- Token refresh and validation (existing core endpoints)
- User profile retrieval (existing core endpoints)

#### **Authorization Endpoints (Staff App)**

- User permissions and role information
- Permission validation for frontend
- Enhanced role abstractions

#### **Role Management Endpoints (Staff Only)**

- Role CRUD operations with enhanced functionality
- User-to-role assignment/removal
- Permission-to-role assignment with audit trails
- Role membership and hierarchy management

#### **Staff Management Endpoints (Staff Managers Only)**

- Staff user creation and onboarding
- Staff profile management
- Organizational hierarchy management
- Department and team oversight

#### **Business Domain Endpoints**

- Product Management APIs (with staff authorization)
- Order Management APIs (with staff authorization)
- Customer Management APIs (with staff authorization)
- Content Management APIs (with staff authorization)
- Analytics and Reporting APIs (with staff authorization)

#### **System Administration Endpoints (Superuser Only)**

- System-level user management
- System health and monitoring
- Comprehensive audit log access
- Permission system management
- Role and organizational structure oversight

### **Permission Enforcement Strategy**

#### **API Level**

- **Permission-based DRF permission classes** (no hardcoded role names)
- Granular permission checking for create, read, update, delete operations
- Custom permissions for specialized operations (staff toggle, audit viewing)
- Automatic permission validation on all endpoints
- Staff management permission enforcement with organizational context

#### **View Level**

- Permission-based access control in viewsets with organizational hierarchy
- Method-level permission granularity with action-specific checks
- Dynamic permission checking based on user permissions and department
- Staff profile-aware permission checking with circular dependency prevention
- Consistent service layer usage for all business logic

#### **Object Level**

- Resource-specific access control where needed
- Owner-based permissions for user-generated content
- Contextual permission checking

### **Security Features**

#### **Authentication Security**

- JWT token-based authentication
- Token expiration and refresh mechanism
- Secure token storage and transmission

#### **Authorization Security**

- Explicit permission checking on all operations
- Group membership validation
- Action-based permission enforcement

#### **Audit & Monitoring**

- Comprehensive API access logging with enhanced detail
- Permission and role change tracking
- Staff management action logging
- Security event monitoring with threat detection
- Failed access attempt logging and analysis
- Organizational change audit trails

---

## **Integration Points**

### **Existing System Integration**

- Seamless integration with current User/Customer models
- Backward compatibility with existing permission checks
- Gradual migration from current staff system
- Preservation of existing business logic

### **Frontend Integration**

- React Admin interface consuming enhanced DRF APIs
- Role-based UI component rendering with organizational context
- Dynamic menu generation based on permissions and staff level
- Real-time permission validation with staff profile awareness
- Staff management interfaces for authorized users

### **External System Integration**

- API endpoints for third-party integrations
- Webhook support for permission changes
- Export capabilities for external reporting tools
- Integration-friendly data formats

---

## **Implementation Benefits**

### **Simplicity**

- Easy to understand and maintain
- Clear permission boundaries
- Minimal learning curve for developers
- Straightforward troubleshooting

### **Scalability**

- Built on Django's proven Group system
- Efficient permission checking
- Easy to add new groups and permissions
- Horizontal scaling friendly

### **Security**

- Explicit permission enforcement
- Comprehensive audit trails
- Secure API-only access
- Clear separation of concerns

### **Maintainability**

- Standard Django patterns
- Well-documented API endpoints
- Clear group-permission mappings
- Easy permission updates

---

## **Architectural Improvements (v2.0)**

### **Permission-Based Authorization**

**Problem Solved**: Eliminated hardcoded role name checks that made the system brittle and difficult to maintain.

**Implementation**:

- All permission classes now check for specific permissions instead of role names
- `CanCreateStaff` checks for `staff.add_staffprofile` permission
- `CanManageGroups` has granular checks for `add_group`, `change_group`, `delete_group`
- `CanViewAuditLogs` checks for `core.can_view_audit_logs` permission

**Benefits**:

- Role name changes don't require code changes
- More flexible permission assignment
- Better adherence to principle of least privilege

### **Custom Permissions**

**New Custom Permissions**:

- `core.can_toggle_staff_status`: Delegate staff status toggle from superuser to Staff Managers
- `core.can_manage_staff_roles`: Allow role management without full superuser access
- `core.can_view_audit_logs`: Granular audit log access control

### **Service Layer Consistency**

**Problem Solved**: Business logic was scattered between ViewSets and service classes.

**Implementation**:

- All group operations now go through `GroupService`
- ViewSets are thin layers that call service methods
- Consistent audit logging across all operations
- Better separation of concerns and testability

### **Circular Dependency Prevention**

**Problem Solved**: Staff management hierarchy could create circular dependencies.

**Implementation**:

- Enhanced `StaffProfile.clean()` method with recursive validation
- `_check_circular_management()` prevents complex circular chains
- `get_management_chain()` and `get_all_subordinates()` helper methods
- Safety checks to prevent infinite loops

---

## **Success Criteria**

### **Functional Requirements**

- All staff operations accessible via API
- Clear group-based permission enforcement
- Comprehensive user-to-group management
- Complete audit trail for all actions

### **Technical Requirements**

- Sub-100ms API response times
- 99.9% API availability
- Comprehensive API documentation
- Full Postman collection coverage

### **Security Requirements**

- Zero unauthorized access incidents
- Complete action auditability
- Secure token management
- Proper permission isolation

### **Usability Requirements**

- Intuitive React Admin interface
- Clear error messages and feedback
- Efficient group management workflows
- Easy permission troubleshooting

This enhanced RBAC system (v2.0) provides a robust, maintainable solution that meets complex organizational needs
while maintaining architectural excellence. The system features permission-based authorization, comprehensive staff
management, organizational hierarchies with circular dependency prevention, and enhanced role abstractions while
ensuring security, scalability, and operational efficiency. The architectural improvements address real-world
concerns about maintainability, security, and business logic separation.
