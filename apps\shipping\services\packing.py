from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from decimal import Decimal
import logging
from django.core.cache import cache
from django.db.models import Q

try:
    from py3dbp import Packer, Bin, Item
    PY3DBP_AVAILABLE = True
except ImportError:
    PY3DBP_AVAILABLE = False
    logging.warning("py3dbp not available. Install with: pip install py3dbp")


@dataclass
class PackedItem:
    """Represents a packed item with its details"""
    sku: str
    product_title: str
    quantity: int
    weight: Decimal
    volume: Decimal
    dimensions: Dict[str, Decimal]  # length, width, height


@dataclass
class PackedBox:
    """Represents a packed box with its contents"""
    box: 'Box'
    items: List[PackedItem]
    utilization: float
    total_weight: Decimal
    total_cost: Decimal
    efficiency_score: float


@dataclass
class PackingResult:
    """Result of packing calculation"""
    boxes: List[PackedBox]
    total_cost: Decimal
    total_weight: Decimal
    total_volume: Decimal
    unpacked_items: List[Dict[str, Any]]
    success: bool
    calculation_time: float
    method_used: str
    warnings: List[str]


class PackingService:
    """Service for calculating optimal packaging using 3D bin packing and rules"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.cache_timeout = 3600  # 1 hour
        self.cache_prefix = 'shipping:packing:'
    
    def calculate_optimal_packaging(self, cart_items) -> PackingResult:
        """Main entry point for packaging calculation"""
        import time
        start_time = time.time()
        
        try:
            # Filter physical items only
            physical_items = self._filter_physical_items(cart_items)
            if not physical_items:
                return PackingResult(
                    boxes=[], total_cost=Decimal('0'), total_weight=Decimal('0'),
                    total_volume=Decimal('0'), unpacked_items=[], success=True,
                    calculation_time=time.time() - start_time,
                    method_used='no_physical_items', warnings=[]
                )
            
            # Apply packing rules first
            rule_results = self._apply_packing_rules(physical_items)
            
            # Use 3D bin packing for remaining items
            if rule_results['remaining_items']:
                if PY3DBP_AVAILABLE:
                    packing_result = self._run_3d_bin_packing(rule_results['remaining_items'])
                else:
                    packing_result = self._run_fallback_packing(rule_results['remaining_items'])
            else:
                packing_result = PackingResult(
                    boxes=[], total_cost=Decimal('0'), total_weight=Decimal('0'),
                    total_volume=Decimal('0'), unpacked_items=[], success=True,
                    calculation_time=0, method_used='rules_only', warnings=[]
                )
            
            # Combine rule-based and bin packing results
            final_result = self._combine_results(rule_results, packing_result)
            final_result.calculation_time = time.time() - start_time
            
            return final_result
            
        except Exception as e:
            self.logger.error(f"Packing calculation failed: {e}")
            return self._get_fallback_result(cart_items, time.time() - start_time)
    
    def _filter_physical_items(self, cart_items):
        """Filter out digital products and zero-weight items"""
        physical_items = []
        for item in cart_items:
            product = item.product
            variant = item.product_variant
            
            # Skip digital products
            if product.is_digital:
                continue
            
            # Skip zero-weight items
            if variant.weight <= 0:
                continue
            
            # Skip items with invalid dimensions
            if any(dim <= 0 for dim in [variant.length, variant.width, variant.height]):
                self.logger.warning(f"Item {variant.sku} has invalid dimensions")
                continue
            
            physical_items.append(item)
        
        return physical_items
    
    def _apply_packing_rules(self, items):
        """Apply configured packing rules to items"""
        from ..models import PackingRule
        
        # Get active rules ordered by priority
        rules = PackingRule.objects.filter(is_active=True).order_by('-priority')
        
        processed_items = []
        remaining_items = list(items)
        total_rule_cost = Decimal('0')
        
        for rule in rules:
            if not remaining_items:
                break
            
            # Find items matching this rule
            matching_items = []
            for item in remaining_items[:]:
                if self._item_matches_rule(item, rule):
                    matching_items.append(item)
                    remaining_items.remove(item)
            
            if matching_items:
                # Process matching items according to rule
                rule_result = self._process_rule_items(matching_items, rule)
                processed_items.extend(rule_result['boxes'])
                total_rule_cost += rule_result['cost']
        
        return {
            'boxes': processed_items,
            'remaining_items': remaining_items,
            'cost': total_rule_cost
        }
    
    def _item_matches_rule(self, cart_item, rule):
        """Check if cart item matches packing rule conditions"""
        variant = cart_item.product_variant
        product = cart_item.product
        
        # Calculate item totals
        total_weight = variant.weight * cart_item.quantity
        total_volume = variant.volume * cart_item.quantity
        item_count = cart_item.quantity
        product_types = [product.product_type.id] if product.product_type else []
        
        return rule.matches_conditions(total_weight, total_volume, item_count, product_types)
    
    def _process_rule_items(self, items, rule):
        """Process items according to packing rule"""
        boxes = []
        total_cost = Decimal('0')
        
        if rule.force_mailer:
            # Use cheapest mailer
            mailer = self._get_cheapest_mailer()
            if mailer:
                packed_box = self._create_rule_based_box(items, mailer, rule)
                boxes.append(packed_box)
                total_cost += packed_box.total_cost
        
        elif rule.preferred_box:
            # Use preferred box
            packed_box = self._create_rule_based_box(items, rule.preferred_box, rule)
            boxes.append(packed_box)
            total_cost += packed_box.total_cost
        
        elif rule.force_separate_packaging:
            # Package each item separately
            for item in items:
                best_box = self._find_best_box_for_item(item)
                if best_box:
                    packed_box = self._create_rule_based_box([item], best_box, rule)
                    boxes.append(packed_box)
                    total_cost += packed_box.total_cost
        
        return {'boxes': boxes, 'cost': total_cost}
    
    def _run_3d_bin_packing(self, items):
        """Execute 3D bin packing algorithm using py3dbp"""
        if not PY3DBP_AVAILABLE:
            return self._run_fallback_packing(items)
        
        try:
            packer = Packer()
            available_boxes = self._get_available_boxes()
            
            # Add boxes as bins (sorted by efficiency)
            for box in sorted(available_boxes, key=lambda b: b.get_efficiency_ratio()):
                bin_obj = Bin(
                    name=f"box_{box.id}",
                    width=float(box.internal_width),
                    height=float(box.internal_height),
                    depth=float(box.internal_length),
                    max_weight=float(box.max_weight)
                )
                packer.addBin(bin_obj)
            
            # Add items with quantity expansion
            item_map = {}
            for cart_item in items:
                variant = cart_item.product_variant
                for i in range(cart_item.quantity):
                    item_id = f"{variant.sku}_{i}"
                    item_obj = Item(
                        name=item_id,
                        width=float(variant.width),
                        height=float(variant.height),
                        depth=float(variant.length),
                        weight=float(variant.weight)
                    )
                    packer.addItem(item_obj)
                    item_map[item_id] = cart_item
            
            # Execute packing
            packer.pack()
            
            return self._process_3d_packing_result(packer, available_boxes, item_map)
            
        except Exception as e:
            self.logger.error(f"3D bin packing failed: {e}")
            return self._run_fallback_packing(items)
    
    def _run_fallback_packing(self, items):
        """Fallback packing method when 3D bin packing is not available"""
        boxes = []
        unpacked_items = []
        total_cost = Decimal('0')
        total_weight = Decimal('0')
        total_volume = Decimal('0')
        
        for cart_item in items:
            best_box = self._find_best_box_for_item(cart_item)
            if best_box:
                packed_box = self._create_simple_packed_box(cart_item, best_box)
                boxes.append(packed_box)
                total_cost += packed_box.total_cost
                total_weight += packed_box.total_weight
                total_volume += best_box.volume
            else:
                unpacked_items.append({
                    'sku': cart_item.product_variant.sku,
                    'reason': 'No suitable box found'
                })
        
        return PackingResult(
            boxes=boxes,
            total_cost=total_cost,
            total_weight=total_weight,
            total_volume=total_volume,
            unpacked_items=unpacked_items,
            success=len(unpacked_items) == 0,
            calculation_time=0,
            method_used='fallback_simple',
            warnings=['3D bin packing not available, using simple fallback']
        )
    
    def _get_available_boxes(self):
        """Get available boxes from cache or database"""
        cache_key = f"{self.cache_prefix}boxes"
        boxes = cache.get(cache_key)
        
        if boxes is None:
            from ..models import Box
            boxes = list(Box.objects.filter(is_active=True).order_by('volume', 'cost'))
            cache.set(cache_key, boxes, self.cache_timeout)
        
        return boxes
    
    def _get_cheapest_mailer(self):
        """Get the cheapest available mailer"""
        from ..models import Box
        return Box.objects.filter(is_active=True, is_mailer=True).order_by('cost').first()
    
    def _find_best_box_for_item(self, cart_item):
        """Find the best box for a single cart item"""
        variant = cart_item.product_variant
        total_weight = variant.weight * cart_item.quantity
        
        available_boxes = self._get_available_boxes()
        
        # Find boxes that can fit the item
        suitable_boxes = []
        for box in available_boxes:
            if (box.can_fit_weight(total_weight) and 
                box.can_fit_dimensions(variant.length, variant.width, variant.height)):
                suitable_boxes.append(box)
        
        if not suitable_boxes:
            return None
        
        # Return the most efficient box (best cost per volume ratio)
        return min(suitable_boxes, key=lambda b: b.get_efficiency_ratio())
    
    def _create_rule_based_box(self, items, box, rule):
        """Create a packed box based on rule processing"""
        packed_items = []
        total_weight = Decimal('0')
        total_volume = Decimal('0')
        
        for cart_item in items:
            variant = cart_item.product_variant
            item_weight = variant.weight * cart_item.quantity
            item_volume = variant.volume * cart_item.quantity
            
            packed_item = PackedItem(
                sku=variant.sku,
                product_title=cart_item.product.title,
                quantity=cart_item.quantity,
                weight=item_weight,
                volume=item_volume,
                dimensions={
                    'length': variant.length,
                    'width': variant.width,
                    'height': variant.height
                }
            )
            packed_items.append(packed_item)
            total_weight += item_weight
            total_volume += item_volume
        
        # Calculate utilization
        utilization = float(total_volume / box.volume * 100) if box.volume > 0 else 0
        
        # Apply rule cost modifications
        base_cost = box.cost
        final_cost = rule.apply_rule(base_cost)
        
        return PackedBox(
            box=box,
            items=packed_items,
            utilization=min(utilization, 100.0),
            total_weight=total_weight,
            total_cost=final_cost,
            efficiency_score=float(final_cost / total_volume) if total_volume > 0 else float('inf')
        )

    def _create_simple_packed_box(self, cart_item, box):
        """Create a simple packed box for fallback method"""
        variant = cart_item.product_variant
        item_weight = variant.weight * cart_item.quantity
        item_volume = variant.volume * cart_item.quantity

        packed_item = PackedItem(
            sku=variant.sku,
            product_title=cart_item.product.title,
            quantity=cart_item.quantity,
            weight=item_weight,
            volume=item_volume,
            dimensions={
                'length': variant.length,
                'width': variant.width,
                'height': variant.height
            }
        )

        utilization = float(item_volume / box.volume * 100) if box.volume > 0 else 0

        return PackedBox(
            box=box,
            items=[packed_item],
            utilization=min(utilization, 100.0),
            total_weight=item_weight,
            total_cost=box.cost,
            efficiency_score=float(box.cost / item_volume) if item_volume > 0 else float('inf')
        )

    def _process_3d_packing_result(self, packer, available_boxes, item_map):
        """Process py3dbp packing results into our format"""
        packed_boxes = []
        total_cost = Decimal('0')
        total_weight = Decimal('0')
        total_volume = Decimal('0')
        unpacked_items = []

        # Process packed bins
        for bin_obj in packer.bins:
            if bin_obj.items:
                # Find the corresponding box
                box_id = int(bin_obj.name.split('_')[1])
                box = next(b for b in available_boxes if b.id == box_id)

                # Group items by cart item
                item_groups = {}
                bin_weight = Decimal('0')

                for item in bin_obj.items:
                    cart_item = item_map[item.name]
                    sku = cart_item.product_variant.sku

                    if sku not in item_groups:
                        item_groups[sku] = {
                            'cart_item': cart_item,
                            'count': 0,
                            'weight': Decimal('0'),
                            'volume': Decimal('0')
                        }

                    item_groups[sku]['count'] += 1
                    item_groups[sku]['weight'] += Decimal(str(item.weight))
                    item_groups[sku]['volume'] += Decimal(str(item.width * item.height * item.depth))
                    bin_weight += Decimal(str(item.weight))

                # Create packed items
                packed_items = []
                for sku, group in item_groups.items():
                    cart_item = group['cart_item']
                    variant = cart_item.product_variant

                    packed_item = PackedItem(
                        sku=sku,
                        product_title=cart_item.product.title,
                        quantity=group['count'],
                        weight=group['weight'],
                        volume=group['volume'],
                        dimensions={
                            'length': variant.length,
                            'width': variant.width,
                            'height': variant.height
                        }
                    )
                    packed_items.append(packed_item)

                # Calculate utilization
                used_volume = sum(item.width * item.height * item.depth for item in bin_obj.items)
                bin_volume = bin_obj.width * bin_obj.height * bin_obj.depth
                utilization = (used_volume / bin_volume) * 100 if bin_volume > 0 else 0

                packed_box = PackedBox(
                    box=box,
                    items=packed_items,
                    utilization=utilization,
                    total_weight=bin_weight,
                    total_cost=box.cost,
                    efficiency_score=float(box.cost / used_volume) if used_volume > 0 else float('inf')
                )

                packed_boxes.append(packed_box)
                total_cost += box.cost
                total_weight += bin_weight
                total_volume += Decimal(str(used_volume))

        # Handle unpacked items
        for item in packer.unfit_items:
            cart_item = item_map[item.name]
            unpacked_items.append({
                'sku': cart_item.product_variant.sku,
                'reason': 'Could not fit in any available box'
            })

        return PackingResult(
            boxes=packed_boxes,
            total_cost=total_cost,
            total_weight=total_weight,
            total_volume=total_volume,
            unpacked_items=unpacked_items,
            success=len(unpacked_items) == 0,
            calculation_time=0,
            method_used='3d_bin_packing',
            warnings=[]
        )

    def _combine_results(self, rule_results, packing_result):
        """Combine rule-based and bin packing results"""
        all_boxes = rule_results['boxes'] + packing_result.boxes
        total_cost = rule_results['cost'] + packing_result.total_cost
        total_weight = sum(box.total_weight for box in all_boxes)
        total_volume = sum(box.box.volume for box in all_boxes)

        return PackingResult(
            boxes=all_boxes,
            total_cost=total_cost,
            total_weight=total_weight,
            total_volume=total_volume,
            unpacked_items=packing_result.unpacked_items,
            success=len(packing_result.unpacked_items) == 0,
            calculation_time=packing_result.calculation_time,
            method_used='combined_rules_and_packing',
            warnings=packing_result.warnings
        )

    def _get_fallback_result(self, cart_items, calculation_time):
        """Get fallback result when packing calculation fails"""
        # Simple fallback: assume one medium box per item
        fallback_cost = Decimal('5.00') * len(cart_items)
        fallback_weight = sum(
            item.product_variant.weight * item.quantity
            for item in cart_items
            if not item.product.is_digital
        )

        return PackingResult(
            boxes=[],
            total_cost=fallback_cost,
            total_weight=fallback_weight,
            total_volume=Decimal('0'),
            unpacked_items=[],
            success=False,
            calculation_time=calculation_time,
            method_used='fallback_error',
            warnings=['Packing calculation failed, using fallback values']
        )
