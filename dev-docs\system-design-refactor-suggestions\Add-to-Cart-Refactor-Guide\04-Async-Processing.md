# Async Processing Implementation Guide

## Overview

Moving CPU-intensive operations to background tasks is critical for handling high concurrent loads. This guide implements Celery-based async processing for shipping calculations, packing algorithms, and external API calls.

## Current Synchronous Issues

### Blocking Operations
- 3D bin packing calculations (50-200ms CPU time)
- Multiple carrier API calls (100-500ms each)
- Complex shipping calculations (100-300ms)
- Database-heavy operations

### Impact on User Experience
- Long response times (500-2000ms)
- Request timeouts under load
- Poor perceived performance
- System unresponsiveness

## Async Architecture Design

### Task Queue Structure

```mermaid
graph TD
    A[User Request] --> B[Fast Response]
    A --> C[Background Task Queue]
    
    C --> D[High Priority Queue]
    C --> E[Normal Priority Queue]
    C --> F[Low Priority Queue]
    
    D --> G[Cart Operations]
    E --> H[Shipping Calculations]
    F --> I[Analytics/Cleanup]
    
    G --> J[Celery Workers]
    H --> J
    I --> J
    
    J --> K[Redis Broker]
    J --> L[Database Updates]
    J --> M[Cache Updates]
```

### Queue Priority System

**High Priority (< 5 seconds):**
- Cart item additions/updates
- Inventory checks
- User-facing operations

**Normal Priority (< 30 seconds):**
- Shipping calculations
- Packing optimizations
- External API calls

**Low Priority (< 5 minutes):**
- Analytics processing
- Cache warming
- Cleanup operations

## Step-by-Step Implementation

### Phase 1: Celery Setup (Day 1)

#### Step 1: Install and Configure Celery

**1.1 Install Dependencies**
```bash
pip install celery[redis]==5.3.4
pip install flower==2.0.1  # For monitoring
```

**1.2 Celery Configuration**
```python
# pc_hardware/celery.py
import os
from celery import Celery
from django.conf import settings

# Set default Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pc_hardware.settings.dev')

app = Celery('picky_store')

# Configure Celery using Django settings
app.config_from_object('django.conf:settings', namespace='CELERY')

# Auto-discover tasks from all installed apps
app.autodiscover_tasks()

# Task routing configuration
app.conf.task_routes = {
    'apps.cart.tasks.*': {'queue': 'high_priority'},
    'apps.shipping.tasks.*': {'queue': 'normal_priority'},
    'apps.analytics.tasks.*': {'queue': 'low_priority'},
}

# Queue configuration
app.conf.task_default_queue = 'normal_priority'
app.conf.task_queues = {
    'high_priority': {
        'exchange': 'high_priority',
        'routing_key': 'high_priority',
    },
    'normal_priority': {
        'exchange': 'normal_priority',
        'routing_key': 'normal_priority',
    },
    'low_priority': {
        'exchange': 'low_priority',
        'routing_key': 'low_priority',
    },
}

# Performance settings
app.conf.worker_prefetch_multiplier = 1
app.conf.task_acks_late = True
app.conf.worker_max_tasks_per_child = 1000

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
```

**1.3 Django Settings for Celery**
```python
# pc_hardware/settings/common.py

# Celery Configuration
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'

# Task serialization
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_ACCEPT_CONTENT = ['json']

# Timezone configuration
CELERY_TIMEZONE = TIME_ZONE
CELERY_ENABLE_UTC = True

# Task execution settings
CELERY_TASK_ALWAYS_EAGER = False  # Set to True for testing
CELERY_TASK_EAGER_PROPAGATES = True
CELERY_TASK_IGNORE_RESULT = False

# Result backend settings
CELERY_RESULT_EXPIRES = 3600  # 1 hour
CELERY_CACHE_BACKEND = 'django-cache'

# Worker settings
CELERY_WORKER_CONCURRENCY = 4
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000
CELERY_WORKER_DISABLE_RATE_LIMITS = True

# Task retry settings
CELERY_TASK_RETRY_DELAY = 60  # 1 minute
CELERY_TASK_MAX_RETRIES = 3

# Monitoring
CELERY_SEND_TASK_EVENTS = True
CELERY_SEND_EVENTS = True
```

#### Step 2: Create Cart Async Tasks

**2.1 Cart Task Module**
```python
# apps/cart/tasks.py
from celery import shared_task
from celery.utils.log import get_task_logger
from django.core.cache import cache
from .models import Cart, CartItem
from .services.cache import CartCacheService
import time

logger = get_task_logger(__name__)

@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def update_cart_totals_async(self, cart_id):
    """Asynchronously update cart totals and cache"""
    try:
        cart = Cart.objects.get(id=cart_id)
        cache_service = CartCacheService()
        
        # Update cart calculations
        cart.total_weight = cart.get_cart_weight()
        cart.save(update_fields=['total_weight'])
        
        # Update cache
        cache_service.cache_cart_data(cart)
        
        logger.info(f"Updated cart totals for cart {cart_id}")
        return {
            'success': True,
            'cart_id': str(cart_id),
            'total_weight': float(cart.total_weight)
        }
        
    except Cart.DoesNotExist:
        logger.error(f"Cart {cart_id} not found")
        return {'success': False, 'error': 'Cart not found'}
    
    except Exception as exc:
        logger.error(f"Failed to update cart totals: {exc}")
        self.retry(exc=exc, countdown=60)

@shared_task(bind=True, max_retries=2)
def validate_cart_items_async(self, cart_id):
    """Asynchronously validate cart items (stock, pricing, etc.)"""
    try:
        cart = Cart.objects.prefetch_related(
            'cart_items__product_variant'
        ).get(id=cart_id)
        
        validation_results = []
        
        for item in cart.cart_items.all():
            # Check stock availability
            if item.product_variant.stock_qty < item.quantity:
                validation_results.append({
                    'item_id': item.id,
                    'issue': 'insufficient_stock',
                    'available': item.product_variant.stock_qty,
                    'requested': item.quantity
                })
            
            # Check if product is still active
            if not item.product_variant.is_active or not item.product.is_active:
                validation_results.append({
                    'item_id': item.id,
                    'issue': 'product_inactive'
                })
        
        # Cache validation results
        cache_key = f"cart_validation:{cart_id}"
        cache.set(cache_key, validation_results, 300)  # 5 minutes
        
        return {
            'success': True,
            'cart_id': str(cart_id),
            'issues': validation_results
        }
        
    except Exception as exc:
        logger.error(f"Cart validation failed: {exc}")
        self.retry(exc=exc, countdown=30)

@shared_task(queue='high_priority')
def process_cart_item_addition(cart_id, product_variant_id, quantity):
    """Process cart item addition with optimistic locking"""
    try:
        from django.db import transaction
        
        with transaction.atomic():
            cart = Cart.objects.select_for_update().get(id=cart_id)
            
            # Add or update cart item
            cart_item, created = CartItem.objects.get_or_create(
                cart=cart,
                product_variant_id=product_variant_id,
                defaults={'quantity': quantity}
            )
            
            if not created:
                cart_item.quantity += quantity
                cart_item.save()
            
            # Trigger async updates
            update_cart_totals_async.delay(cart_id)
            validate_cart_items_async.delay(cart_id)
            
            return {
                'success': True,
                'cart_item_id': cart_item.id,
                'new_quantity': cart_item.quantity,
                'created': created
            }
            
    except Exception as exc:
        logger.error(f"Failed to process cart item addition: {exc}")
        return {'success': False, 'error': str(exc)}
```

### Phase 2: Shipping Async Tasks (Day 2-3)

#### Step 3: Implement Shipping Background Tasks

**3.1 Shipping Task Module**
```python
# apps/shipping/tasks.py
from celery import shared_task, group
from celery.utils.log import get_task_logger
from django.core.cache import cache
from .services.cart import CartShippingService
from .services.shipping import ShippingService
from .services.packing import PackingService
from .services.cache import ShippingCacheService
from apps.cart.models import Cart
import time

logger = get_task_logger(__name__)

@shared_task(bind=True, max_retries=3, default_retry_delay=120)
def calculate_shipping_async(self, cart_id, force_recalculate=False):
    """Asynchronously calculate shipping for a cart"""
    try:
        start_time = time.time()
        
        cart = Cart.objects.prefetch_related(
            'cart_items__product_variant'
        ).get(id=cart_id)
        
        shipping_service = CartShippingService()
        result = shipping_service.recalculate_cart_shipping(
            cart, force_recalculate=force_recalculate
        )
        
        calculation_time = time.time() - start_time
        
        # Store result in cache for immediate retrieval
        cache_key = f"shipping_calculation:{cart_id}"
        cache.set(cache_key, {
            'result': result,
            'calculation_time': calculation_time,
            'calculated_at': time.time()
        }, 300)  # 5 minutes
        
        logger.info(f"Shipping calculation completed for cart {cart_id} in {calculation_time:.2f}s")
        
        return {
            'success': True,
            'cart_id': str(cart_id),
            'calculation_time': calculation_time,
            'result': result
        }
        
    except Cart.DoesNotExist:
        logger.error(f"Cart {cart_id} not found for shipping calculation")
        return {'success': False, 'error': 'Cart not found'}
    
    except Exception as exc:
        logger.error(f"Shipping calculation failed for cart {cart_id}: {exc}")
        self.retry(exc=exc, countdown=120)

@shared_task(bind=True, max_retries=2)
def calculate_packing_async(self, cart_id):
    """Asynchronously calculate optimal packing"""
    try:
        cart = Cart.objects.prefetch_related(
            'cart_items__product_variant__product__product_type'
        ).get(id=cart_id)
        
        cart_items = cart.cart_items.all()
        if not cart_items:
            return {'success': True, 'message': 'No items to pack'}
        
        packing_service = PackingService()
        packing_result = packing_service.calculate_optimal_packaging(cart_items)
        
        # Cache packing result
        cache_key = f"packing_result:{cart_id}"
        cache.set(cache_key, {
            'result': packing_result,
            'calculated_at': time.time()
        }, 600)  # 10 minutes
        
        return {
            'success': True,
            'cart_id': str(cart_id),
            'total_cost': float(packing_result.total_cost),
            'total_weight': float(packing_result.total_weight),
            'calculation_time': packing_result.calculation_time
        }
        
    except Exception as exc:
        logger.error(f"Packing calculation failed: {exc}")
        self.retry(exc=exc, countdown=60)

@shared_task(bind=True, max_retries=3)
def get_carrier_rates_async(self, packing_result_data, destination_data):
    """Asynchronously get rates from all carriers"""
    try:
        from .services.carriers import get_carrier_instance
        from .models import Carrier
        
        # Reconstruct objects from serialized data
        # (Implementation depends on your serialization strategy)
        
        carriers = Carrier.objects.filter(is_active=True)
        rate_tasks = []
        
        # Create parallel tasks for each carrier
        for carrier_config in carriers:
            rate_tasks.append(
                get_single_carrier_rate.s(
                    carrier_config.id,
                    packing_result_data,
                    destination_data
                )
            )
        
        # Execute carrier calls in parallel
        job = group(rate_tasks)
        result = job.apply_async()
        
        # Collect results
        rates = []
        for task_result in result.get(timeout=30):
            if task_result['success']:
                rates.append(task_result['rate'])
        
        return {
            'success': True,
            'rates': rates,
            'carrier_count': len(carriers)
        }
        
    except Exception as exc:
        logger.error(f"Carrier rate collection failed: {exc}")
        self.retry(exc=exc, countdown=60)

@shared_task(bind=True, max_retries=2, time_limit=30)
def get_single_carrier_rate(self, carrier_id, packing_result_data, destination_data):
    """Get rate from a single carrier with timeout"""
    try:
        from .models import Carrier
        from .services.carriers import get_carrier_instance
        
        carrier_config = Carrier.objects.get(id=carrier_id)
        carrier = get_carrier_instance(carrier_config)
        
        # Reconstruct objects and get rate
        # (Implementation depends on your data structures)
        
        rate = carrier.get_shipping_rate(packing_result, destination)
        
        return {
            'success': True,
            'carrier_id': carrier_id,
            'carrier_name': carrier_config.title,
            'rate': rate
        }
        
    except Exception as exc:
        logger.warning(f"Carrier {carrier_id} rate fetch failed: {exc}")
        return {
            'success': False,
            'carrier_id': carrier_id,
            'error': str(exc)
        }
```

#### Step 4: Update Cart Operations for Async Processing

**4.1 Async Cart Serializer**
```python
# apps/cart/serializers.py - Updated for async processing
from .tasks import calculate_shipping_async, process_cart_item_addition
from django.core.cache import cache

class AddCartItemSerializer(ModelSerializer):
    def save(self, **kwargs):
        cart_id = self.context['cart_id']
        product_variant = self.validated_data['product_variant']
        quantity = self.validated_data['quantity']
        
        # Process addition asynchronously for better performance
        task_result = process_cart_item_addition.delay(
            cart_id, product_variant.id, quantity
        )
        
        # For immediate response, create a minimal cart item
        try:
            cart = Cart.objects.get(id=cart_id)
        except Cart.DoesNotExist:
            cart = Cart.objects.create(id=cart_id)
        
        # Return optimistic response
        self.instance = {
            'cart_id': cart_id,
            'product_variant_id': product_variant.id,
            'quantity': quantity,
            'task_id': task_result.id,
            'status': 'processing'
        }
        
        # Trigger async shipping calculation
        calculate_shipping_async.delay(cart_id, force_recalculate=True)
        
        return self.instance

class AsyncCartSerializer(ModelSerializer):
    """Serializer that handles async operations"""
    
    shipping_status = serializers.SerializerMethodField()
    calculation_progress = serializers.SerializerMethodField()
    
    def get_shipping_status(self, obj):
        """Get shipping calculation status"""
        cache_key = f"shipping_calculation:{obj.id}"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            return {
                'status': 'completed',
                'calculated_at': cached_result['calculated_at'],
                'calculation_time': cached_result['calculation_time']
            }
        
        # Check if calculation is in progress
        # (Implementation depends on your task tracking strategy)
        return {'status': 'pending'}
    
    def get_calculation_progress(self, obj):
        """Get overall calculation progress"""
        shipping_status = self.get_shipping_status(obj)
        
        progress = {
            'shipping_calculation': shipping_status['status'],
            'overall_progress': 0
        }
        
        if shipping_status['status'] == 'completed':
            progress['overall_progress'] = 100
        elif shipping_status['status'] == 'pending':
            progress['overall_progress'] = 50
        
        return progress
    
    class Meta:
        model = Cart
        fields = ['id', 'cart_items', 'total_weight', 'shipping_cost',
                 'packing_cost', 'shipping_status', 'calculation_progress']
```

### Phase 3: API Endpoints for Async Operations (Day 4)

#### Step 5: Create Async API Endpoints

**5.1 Async Cart Views**
```python
# apps/cart/views.py - Updated with async support
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from .tasks import calculate_shipping_async
from django.core.cache import cache
import uuid

class CartViewSet(CreateModelMixin, RetrieveModelMixin, DestroyModelMixin, GenericViewSet):
    
    @action(detail=True, methods=['post'])
    def calculate_shipping(self, request, pk=None):
        """Trigger async shipping calculation"""
        cart = self.get_object()
        force_recalculate = request.data.get('force_recalculate', False)
        
        # Start async calculation
        task = calculate_shipping_async.delay(cart.id, force_recalculate)
        
        return Response({
            'task_id': task.id,
            'status': 'started',
            'message': 'Shipping calculation started',
            'poll_url': f'/api/cart/{cart.id}/shipping-status/{task.id}/'
        }, status=status.HTTP_202_ACCEPTED)
    
    @action(detail=True, methods=['get'], url_path='shipping-status/(?P<task_id>[^/.]+)')
    def shipping_status(self, request, pk=None, task_id=None):
        """Get shipping calculation status"""
        from celery.result import AsyncResult
        
        task_result = AsyncResult(task_id)
        
        if task_result.ready():
            if task_result.successful():
                result = task_result.result
                return Response({
                    'status': 'completed',
                    'result': result,
                    'task_id': task_id
                })
            else:
                return Response({
                    'status': 'failed',
                    'error': str(task_result.result),
                    'task_id': task_id
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            return Response({
                'status': 'pending',
                'task_id': task_id,
                'message': 'Calculation in progress'
            })
    
    @action(detail=True, methods=['get'])
    def quick_status(self, request, pk=None):
        """Get quick cart status without triggering calculations"""
        cart = self.get_object()
        
        # Check cached results
        shipping_cache_key = f"shipping_calculation:{cart.id}"
        shipping_result = cache.get(shipping_cache_key)
        
        packing_cache_key = f"packing_result:{cart.id}"
        packing_result = cache.get(packing_cache_key)
        
        return Response({
            'cart_id': str(cart.id),
            'shipping_calculated': shipping_result is not None,
            'packing_calculated': packing_result is not None,
            'last_shipping_calculation': cart.last_shipping_calculation,
            'item_count': cart.cart_items.count(),
            'cached_results_available': {
                'shipping': shipping_result is not None,
                'packing': packing_result is not None
            }
        })
```

**5.2 WebSocket Support for Real-time Updates**
```python
# apps/cart/consumers.py - WebSocket consumer for real-time updates
import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import Cart

class CartUpdatesConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.cart_id = self.scope['url_route']['kwargs']['cart_id']
        self.cart_group_name = f'cart_{self.cart_id}'
        
        # Join cart group
        await self.channel_layer.group_add(
            self.cart_group_name,
            self.channel_name
        )
        
        await self.accept()
    
    async def disconnect(self, close_code):
        # Leave cart group
        await self.channel_layer.group_discard(
            self.cart_group_name,
            self.channel_name
        )
    
    async def cart_update(self, event):
        """Send cart update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'cart_update',
            'data': event['data']
        }))
    
    async def shipping_calculated(self, event):
        """Send shipping calculation result"""
        await self.send(text_data=json.dumps({
            'type': 'shipping_calculated',
            'data': event['data']
        }))
```

## Performance Monitoring

### Task Monitoring Setup

**Celery Monitoring with Flower:**
```bash
# Start Flower monitoring
celery -A pc_hardware flower --port=5555

# Access monitoring dashboard
# http://localhost:5555
```

**Custom Task Monitoring:**
```python
# apps/core/monitoring/task_monitor.py
from celery import current_app
from celery.events.state import State
import time

class TaskMonitor:
    def __init__(self):
        self.app = current_app
        self.state = State()
    
    def get_active_tasks(self):
        """Get currently active tasks"""
        inspect = self.app.control.inspect()
        active_tasks = inspect.active()
        
        return active_tasks
    
    def get_queue_lengths(self):
        """Get queue lengths for monitoring"""
        inspect = self.app.control.inspect()
        
        # Get reserved tasks (queued)
        reserved = inspect.reserved()
        
        queue_lengths = {}
        for worker, tasks in reserved.items():
            for task in tasks:
                queue = task.get('delivery_info', {}).get('routing_key', 'default')
                queue_lengths[queue] = queue_lengths.get(queue, 0) + 1
        
        return queue_lengths
    
    def get_task_stats(self):
        """Get task execution statistics"""
        inspect = self.app.control.inspect()
        stats = inspect.stats()
        
        return stats
```

## Expected Performance Improvements

### Response Time Improvements

| Operation | Synchronous | Async | Improvement |
|-----------|-------------|-------|-------------|
| Add to Cart | 800ms | 50ms | 16x faster |
| Shipping Calculation | 1,500ms | 100ms* | 15x faster |
| Cart Updates | 600ms | 40ms | 15x faster |

*Initial response time; full calculation completes in background

### Throughput Improvements

| Metric | Before Async | After Async | Improvement |
|--------|-------------|-------------|-------------|
| Concurrent Users | 50 | 500+ | 10x increase |
| Requests/Second | 41 | 400+ | 10x increase |
| CPU Utilization | 90% | 30% | 3x reduction |
| Response Time P95 | 2,500ms | 150ms | 17x faster |

### User Experience Benefits

1. **Immediate Feedback**: Users get instant responses
2. **Progressive Loading**: Results appear as they're calculated
3. **Better Reliability**: Failed calculations don't block user actions
4. **Scalable Architecture**: System can handle much higher loads

## Next Steps

1. **Deploy Celery workers** with proper monitoring
2. **Implement WebSocket updates** for real-time feedback
3. **Add circuit breakers** for external API calls
4. **Monitor task performance** and optimize queue configurations
5. **Proceed to Microservices Architecture** (05-Microservices-Architecture.md) for further scaling
