from rest_framework import serializers
from decimal import Decimal
from .models import Box, Carrier, CarrierService, PackingRule
from apps.products.models import ProductType


class BoxSerializer(serializers.ModelSerializer):
    """Serializer for the Box model"""

    volume = serializers.DecimalField(max_digits=12, decimal_places=4, read_only=True)
    efficiency_ratio = serializers.SerializerMethodField()

    class Meta:
        model = Box
        fields = [
            'id', 'title', 'internal_length', 'internal_width', 'internal_height',
            'max_weight', 'cost', 'volume', 'is_mailer', 'is_active', 'priority',
            'efficiency_ratio', 'created_at', 'updated_at'
        ]
        read_only_fields = ['volume', 'created_at', 'updated_at']

    def get_efficiency_ratio(self, obj):
        """Get cost per cubic centimeter"""
        return obj.get_efficiency_ratio()

    def validate(self, data):
        """Validate box dimensions and constraints"""
        # Ensure all dimensions are positive
        for field in ['internal_length', 'internal_width', 'internal_height']:
            if field in data and data[field] <= 0:
                raise serializers.ValidationError(f"{field} must be greater than 0")

        # Ensure max_weight is positive
        if 'max_weight' in data and data['max_weight'] <= 0:
            raise serializers.ValidationError("max_weight must be greater than 0")

        # Ensure cost is non-negative
        if 'cost' in data and data['cost'] < 0:
            raise serializers.ValidationError("cost cannot be negative")

        return data


class CarrierServiceSerializer(serializers.ModelSerializer):
    """Serializer for CarrierService model"""

    delivery_range = serializers.SerializerMethodField()

    class Meta:
        model = CarrierService
        fields = [
            'id', 'service_name', 'service_code', 'is_active',
            'estimated_days', 'max_days', 'delivery_range', 'cost_multiplier',
            'supports_insurance', 'supports_signature'
        ]

    def get_delivery_range(self, obj):
        """Get delivery time range as string"""
        return obj.get_estimated_delivery_range()


class CarrierSerializer(serializers.ModelSerializer):
    """Serializer for Carrier model"""

    services = CarrierServiceSerializer(many=True, read_only=True)
    service_count = serializers.SerializerMethodField()

    class Meta:
        model = Carrier
        fields = [
            'id', 'title', 'code', 'is_active', 'api_endpoint',
            'base_cost', 'priority', 'supports_tracking', 'max_weight',
            'services', 'service_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
        extra_kwargs = {
            'api_key': {'write_only': True},
            'api_secret': {'write_only': True}
        }

    def get_service_count(self, obj):
        """Get count of active services"""
        return obj.services.filter(is_active=True).count()


class ProductTypeSimpleSerializer(serializers.ModelSerializer):
    """Simple serializer for ProductType"""

    class Meta:
        model = ProductType
        fields = ['id', 'title']


class PackingRuleSerializer(serializers.ModelSerializer):
    """Serializer for PackingRule model"""

    product_types = ProductTypeSimpleSerializer(many=True, read_only=True)
    product_type_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    preferred_box_title = serializers.CharField(source='preferred_box.title', read_only=True)
    action_summary = serializers.SerializerMethodField()

    class Meta:
        model = PackingRule
        fields = [
            'id', 'title', 'description', 'priority', 'is_active',
            'min_weight', 'max_weight', 'min_volume', 'max_volume',
            'min_items', 'max_items', 'product_types', 'product_type_ids',
            'preferred_box', 'preferred_box_title', 'force_mailer',
            'force_separate_packaging', 'additional_cost', 'cost_multiplier',
            'action_summary', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_action_summary(self, obj):
        """Get human-readable summary of rule actions"""
        return obj.get_action_summary()

    def create(self, validated_data):
        """Create packing rule with product types"""
        product_type_ids = validated_data.pop('product_type_ids', [])
        rule = PackingRule.objects.create(**validated_data)

        if product_type_ids:
            rule.product_types.set(product_type_ids)

        return rule

    def update(self, instance, validated_data):
        """Update packing rule with product types"""
        product_type_ids = validated_data.pop('product_type_ids', None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        if product_type_ids is not None:
            instance.product_types.set(product_type_ids)

        return instance

    def validate(self, data):
        """Validate packing rule constraints"""
        # Validate weight constraints
        min_weight = data.get('min_weight')
        max_weight = data.get('max_weight')
        if min_weight is not None and max_weight is not None and min_weight > max_weight:
            raise serializers.ValidationError("min_weight cannot be greater than max_weight")

        # Validate volume constraints
        min_volume = data.get('min_volume')
        max_volume = data.get('max_volume')
        if min_volume is not None and max_volume is not None and min_volume > max_volume:
            raise serializers.ValidationError("min_volume cannot be greater than max_volume")

        # Validate item count constraints
        min_items = data.get('min_items')
        max_items = data.get('max_items')
        if min_items is not None and max_items is not None and min_items > max_items:
            raise serializers.ValidationError("min_items cannot be greater than max_items")

        return data


class ShippingCalculationRequestSerializer(serializers.Serializer):
    """Serializer for shipping calculation requests"""

    cart_id = serializers.UUIDField(required=True)
    force_recalculate = serializers.BooleanField(default=False)
    destination_address = serializers.DictField(required=False)


class ShippingCalculationResponseSerializer(serializers.Serializer):
    """Serializer for shipping calculation responses"""

    success = serializers.BooleanField()
    message = serializers.CharField()
    packing_cost = serializers.DecimalField(max_digits=6, decimal_places=2)
    shipping_cost = serializers.DecimalField(max_digits=6, decimal_places=2)
    total_weight = serializers.DecimalField(max_digits=8, decimal_places=2)
    total_volume = serializers.DecimalField(max_digits=12, decimal_places=4)
    packing_result = serializers.DictField(required=False)
    shipping_details = serializers.DictField(required=False)
    error = serializers.CharField(required=False)


class PackedItemSerializer(serializers.Serializer):
    """Serializer for packed items"""

    sku = serializers.CharField()
    product_title = serializers.CharField()
    quantity = serializers.IntegerField()
    weight = serializers.DecimalField(max_digits=8, decimal_places=2)
    volume = serializers.DecimalField(max_digits=12, decimal_places=4)
    dimensions = serializers.DictField()


class PackedBoxSerializer(serializers.Serializer):
    """Serializer for packed boxes"""

    box = BoxSerializer()
    items = PackedItemSerializer(many=True)
    utilization = serializers.FloatField()
    total_weight = serializers.DecimalField(max_digits=8, decimal_places=2)
    total_cost = serializers.DecimalField(max_digits=6, decimal_places=2)
    efficiency_score = serializers.FloatField()


class PackingResultSerializer(serializers.Serializer):
    """Serializer for packing results"""

    boxes = PackedBoxSerializer(many=True)
    total_cost = serializers.DecimalField(max_digits=6, decimal_places=2)
    total_weight = serializers.DecimalField(max_digits=8, decimal_places=2)
    total_volume = serializers.DecimalField(max_digits=12, decimal_places=4)
    unpacked_items = serializers.ListField()
    success = serializers.BooleanField()
    calculation_time = serializers.FloatField()
    method_used = serializers.CharField()
    warnings = serializers.ListField()


class ShippingRateSerializer(serializers.Serializer):
    """Serializer for shipping rates"""

    carrier_name = serializers.CharField()
    service_name = serializers.CharField()
    cost = serializers.DecimalField(max_digits=6, decimal_places=2)
    estimated_days = serializers.IntegerField()
    max_days = serializers.IntegerField()
    tracking_available = serializers.BooleanField()
    insurance_available = serializers.BooleanField()
    signature_available = serializers.BooleanField()


class ShippingOptionsResponseSerializer(serializers.Serializer):
    """Serializer for shipping options response"""

    success = serializers.BooleanField()
    options = ShippingRateSerializer(many=True)
    packing_cost = serializers.DecimalField(max_digits=6, decimal_places=2)
    message = serializers.CharField()
    error = serializers.CharField(required=False)


class CarrierStatusSerializer(serializers.Serializer):
    """Serializer for carrier status"""

    id = serializers.IntegerField()
    title = serializers.CharField()
    code = serializers.CharField()
    is_active = serializers.BooleanField()
    status = serializers.CharField()
    error = serializers.CharField(required=False)
    last_checked = serializers.DateTimeField()


class ShippingAnalyticsSerializer(serializers.Serializer):
    """Serializer for shipping analytics"""

    period = serializers.DictField()
    summary = serializers.DictField()
    trends = serializers.ListField()


class BoxUtilizationSerializer(serializers.Serializer):
    """Serializer for box utilization analytics"""

    box_usage = serializers.ListField()
    efficiency_metrics = serializers.DictField()
