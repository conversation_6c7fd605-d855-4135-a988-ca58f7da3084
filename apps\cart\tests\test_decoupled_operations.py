"""
Tests for the new decoupled cart-shipping architecture.

These tests verify that:
1. Cart operations are fast and don't trigger shipping calculations
2. Shipping calculations work correctly when explicitly requested
3. Weight validation still works without shipping calculations
4. API responses have the correct format
"""

import time
from decimal import Decimal
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from model_bakery import baker

from apps.cart.models import Cart, CartItem
from apps.products.models import Product, ProductVariant, Brand, Category, ProductType
from apps.customers.models import Customer, Address


class DecoupledCartOperationsTest(APITestCase):
    """Test cart operations without shipping calculations"""
    
    def setUp(self):
        # Create test data
        self.brand = baker.make(Brand)
        self.category = baker.make(Category)
        self.product_type = baker.make(ProductType)
        
        self.product = baker.make(
            Product,
            brand=self.brand,
            category=self.category,
            product_type=self.product_type,
            is_active=True
        )
        
        self.variant = baker.make(
            ProductVariant,
            product=self.product,
            price=Decimal('29.99'),
            weight=Decimal('150.00'),  # 150g
            is_active=True
        )
        
        self.cart = baker.make(Cart)
        self.customer = baker.make(Customer)
        self.address = baker.make(Address, customer=self.customer)
    
    def test_add_item_fast_response(self):
        """Test that adding items is fast and doesn't include shipping"""
        url = reverse('cart-items-list', kwargs={'cart_pk': self.cart.id})
        
        data = {
            'product_id': self.product.id,
            'product_variant': self.variant.id,
            'quantity': 2
        }
        
        start_time = time.time()
        response = self.client.post(url, data, format='json')
        response_time = time.time() - start_time
        
        # Should be fast (under 1 second for test environment)
        self.assertLess(response_time, 1.0)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify cart item was created
        self.assertTrue(CartItem.objects.filter(cart=self.cart, product=self.product).exists())
    
    def test_cart_response_excludes_shipping_fields(self):
        """Test that cart response doesn't include shipping-related fields"""
        # Add an item to the cart
        baker.make(CartItem, cart=self.cart, product=self.product, product_variant=self.variant, quantity=1)
        
        url = reverse('carts-detail', kwargs={'pk': self.cart.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Should include basic cart fields
        self.assertIn('id', data)
        self.assertIn('cart_items', data)
        self.assertIn('total_price', data)
        self.assertIn('cart_weight', data)
        self.assertIn('item_count', data)
        
        # Should NOT include shipping-related fields
        self.assertNotIn('shipping_cost', data)
        self.assertNotIn('packing_cost', data)
        self.assertNotIn('grand_total', data)
        self.assertNotIn('total_volume', data)
        self.assertNotIn('packing_details', data)
    
    def test_weight_validation_still_works(self):
        """Test that weight validation works without shipping calculations"""
        url = reverse('cart-items-list', kwargs={'cart_pk': self.cart.id})
        
        # Create a heavy variant (over 20kg limit)
        heavy_variant = baker.make(
            ProductVariant,
            product=self.product,
            weight=Decimal('25000.00'),  # 25kg
            is_active=True
        )
        
        data = {
            'product_id': self.product.id,
            'product_variant': heavy_variant.id,
            'quantity': 1
        }
        
        response = self.client.post(url, data, format='json')
        
        # Should reject due to weight limit
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('weight limit', str(response.data))
    
    def test_update_quantity_fast_response(self):
        """Test that updating quantity is fast and doesn't trigger shipping"""
        # Create cart item
        cart_item = baker.make(
            CartItem, 
            cart=self.cart, 
            product=self.product, 
            product_variant=self.variant, 
            quantity=1
        )
        
        url = reverse('cart-items-detail', kwargs={'cart_pk': self.cart.id, 'pk': cart_item.id})
        
        data = {'quantity': 3}
        
        start_time = time.time()
        response = self.client.patch(url, data, format='json')
        response_time = time.time() - start_time
        
        # Should be fast
        self.assertLess(response_time, 1.0)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify quantity was updated
        cart_item.refresh_from_db()
        self.assertEqual(cart_item.quantity, 3)


class ShippingCalculationTest(APITestCase):
    """Test on-demand shipping calculations"""
    
    def setUp(self):
        # Create test data
        self.brand = baker.make(Brand)
        self.category = baker.make(Category)
        self.product_type = baker.make(ProductType)
        
        self.product = baker.make(
            Product,
            brand=self.brand,
            category=self.category,
            product_type=self.product_type,
            is_active=True
        )
        
        self.variant = baker.make(
            ProductVariant,
            product=self.product,
            price=Decimal('29.99'),
            weight=Decimal('150.00'),
            length=Decimal('10.0'),
            width=Decimal('5.0'),
            height=Decimal('3.0'),
            is_active=True
        )
        
        self.cart = baker.make(Cart)
        self.customer = baker.make(Customer)
        self.address = baker.make(Address, customer=self.customer)
        
        # Add item to cart
        baker.make(
            CartItem,
            cart=self.cart,
            product=self.product,
            product_variant=self.variant,
            quantity=2
        )
    
    def test_shipping_estimate_endpoint(self):
        """Test quick shipping estimate endpoint"""
        url = reverse('cart-items-shipping-estimate', kwargs={'cart_pk': self.cart.id})
        
        start_time = time.time()
        response = self.client.get(url + '?zone=domestic')
        response_time = time.time() - start_time
        
        # Should be very fast (estimate only)
        self.assertLess(response_time, 0.5)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIn('success', data)
        self.assertIn('estimated_cost', data)
        self.assertIn('weight', data)
        self.assertTrue(data['success'])
        self.assertGreater(data['estimated_cost'], 0)
    
    def test_calculate_shipping_with_address_id(self):
        """Test shipping calculation with saved address"""
        # Set cart customer
        self.cart.customer = self.customer
        self.cart.save()
        
        url = reverse('cart-items-calculate-shipping', kwargs={'cart_pk': self.cart.id})
        
        data = {
            'destination_address_id': self.address.id,
            'get_all_options': False
        }
        
        response = self.client.post(url, data, format='json')
        
        # May take longer due to shipping calculation
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        response_data = response.json()
        self.assertIn('cart', response_data)
        self.assertIn('shipping_calculation', response_data)
        
        # Cart should now include shipping fields
        cart_data = response_data['cart']
        self.assertIn('shipping_cost', cart_data)
        self.assertIn('packing_cost', cart_data)
        self.assertIn('grand_total', cart_data)
        
        # Shipping calculation should include details
        shipping_data = response_data['shipping_calculation']
        self.assertIn('success', shipping_data)
        self.assertIn('calculation_time', shipping_data)
        self.assertTrue(shipping_data['success'])
    
    def test_calculate_shipping_with_manual_address(self):
        """Test shipping calculation with manual address"""
        url = reverse('cart-items-calculate-shipping', kwargs={'cart_pk': self.cart.id})
        
        data = {
            'destination_address': {
                'street': '123 Main St',
                'city': 'New York',
                'state': 'NY',
                'zip_code': '10001',
                'country': 'US'
            },
            'get_all_options': True
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        response_data = response.json()
        shipping_data = response_data['shipping_calculation']
        
        # Should include shipping options when get_all_options=True
        self.assertIn('shipping_options', shipping_data)
        self.assertIsInstance(shipping_data['shipping_options'], list)
    
    def test_calculate_shipping_empty_cart(self):
        """Test shipping calculation fails for empty cart"""
        empty_cart = baker.make(Cart)
        
        url = reverse('cart-items-calculate-shipping', kwargs={'cart_pk': empty_cart.id})
        
        data = {
            'destination_address': {
                'street': '123 Main St',
                'city': 'New York',
                'state': 'NY',
                'zip_code': '10001',
                'country': 'US'
            }
        }
        
        response = self.client.post(url, data, format='json')
        
        # Should handle empty cart gracefully
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        response_data = response.json()
        shipping_data = response_data['shipping_calculation']
        self.assertFalse(shipping_data['success'])
        self.assertIn('empty', shipping_data['error'].lower())


class PerformanceTest(APITestCase):
    """Test performance characteristics of the new architecture"""
    
    def setUp(self):
        # Create test data
        self.brand = baker.make(Brand)
        self.category = baker.make(Category)
        self.product_type = baker.make(ProductType)
        
        self.products = []
        self.variants = []
        
        # Create multiple products for testing
        for i in range(5):
            product = baker.make(
                Product,
                brand=self.brand,
                category=self.category,
                product_type=self.product_type,
                is_active=True
            )
            variant = baker.make(
                ProductVariant,
                product=product,
                price=Decimal('19.99'),
                weight=Decimal('100.00'),
                is_active=True
            )
            self.products.append(product)
            self.variants.append(variant)
        
        self.cart = baker.make(Cart)
    
    def test_multiple_cart_operations_performance(self):
        """Test that multiple cart operations remain fast"""
        url = reverse('cart-items-list', kwargs={'cart_pk': self.cart.id})
        
        start_time = time.time()
        
        # Add multiple items
        for i in range(5):
            data = {
                'product_id': self.products[i].id,
                'product_variant': self.variants[i].id,
                'quantity': 1
            }
            response = self.client.post(url, data, format='json')
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        total_time = time.time() - start_time
        
        # All 5 operations should complete quickly
        self.assertLess(total_time, 5.0)  # 5 seconds for 5 operations
        self.assertLess(total_time / 5, 1.0)  # Average under 1 second per operation
        
        # Verify all items were added
        self.assertEqual(CartItem.objects.filter(cart=self.cart).count(), 5)
    
    def test_cart_retrieval_performance(self):
        """Test that cart retrieval is fast even with many items"""
        # Add multiple items to cart
        for i in range(10):
            baker.make(
                CartItem,
                cart=self.cart,
                product=self.products[i % 5],
                product_variant=self.variants[i % 5],
                quantity=1
            )
        
        url = reverse('carts-detail', kwargs={'pk': self.cart.id})
        
        start_time = time.time()
        response = self.client.get(url)
        response_time = time.time() - start_time
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertLess(response_time, 1.0)  # Should be under 1 second
        
        data = response.json()
        self.assertEqual(len(data['cart_items']), 10)
        self.assertGreater(data['total_price'], 0)
        self.assertGreater(data['cart_weight'], 0)
