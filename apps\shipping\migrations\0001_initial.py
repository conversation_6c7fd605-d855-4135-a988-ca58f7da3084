# Generated by Django 5.2.4 on 2025-07-24 13:19

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Box',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Box title/name', max_length=100)),
                ('internal_length', models.DecimalField(decimal_places=2, help_text='Internal length in centimeters', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('internal_width', models.DecimalField(decimal_places=2, help_text='Internal width in centimeters', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('internal_height', models.DecimalField(decimal_places=2, help_text='Internal height in centimeters', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('max_weight', models.DecimalField(decimal_places=2, help_text='Maximum weight capacity in grams', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('1.00'))])),
                ('cost', models.DecimalField(decimal_places=2, help_text='Box cost in USD', max_digits=6, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('volume', models.DecimalField(decimal_places=4, editable=False, help_text='Computed volume in cubic centimeters', max_digits=12)),
                ('is_mailer', models.BooleanField(default=False, help_text='Is this a padded mailer envelope?')),
                ('is_active', models.BooleanField(default=True, help_text='Is this box available for use?')),
                ('priority', models.PositiveIntegerField(default=0, help_text='Priority for box selection (higher = preferred)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Shipping Box',
                'verbose_name_plural': 'Shipping Boxes',
                'ordering': ['priority', 'volume', 'cost'],
            },
        ),
        migrations.CreateModel(
            name='Carrier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Carrier title/name', max_length=100)),
                ('code', models.CharField(help_text="Unique carrier code (e.g., 'posten_bring')", max_length=20, unique=True)),
                ('is_active', models.BooleanField(default=True, help_text='Is this carrier available for use?')),
                ('api_endpoint', models.URLField(blank=True, help_text='API endpoint URL for carrier integration', null=True)),
                ('api_key', models.CharField(blank=True, help_text='API key for carrier authentication', max_length=255, null=True)),
                ('api_secret', models.CharField(blank=True, help_text='API secret for carrier authentication', max_length=255, null=True)),
                ('base_cost', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Base handling cost in USD', max_digits=6, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('priority', models.PositiveIntegerField(default=0, help_text='Priority for carrier selection (higher = preferred)')),
                ('supports_tracking', models.BooleanField(default=True, help_text='Does this carrier support package tracking?')),
                ('max_weight', models.DecimalField(decimal_places=2, default=Decimal('20000.00'), help_text='Maximum weight this carrier can handle (grams)', max_digits=8, validators=[django.core.validators.MinValueValidator(Decimal('1.00'))])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Shipping Carrier',
                'verbose_name_plural': 'Shipping Carriers',
                'ordering': ['-priority', 'title'],
            },
        ),
        migrations.CreateModel(
            name='PackingRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Rule title/name', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Description of what this rule does')),
                ('priority', models.PositiveIntegerField(default=0, help_text='Rule priority (higher = applied first)')),
                ('is_active', models.BooleanField(default=True, help_text='Is this rule active?')),
                ('min_weight', models.DecimalField(blank=True, decimal_places=2, help_text='Minimum total weight in grams', max_digits=8, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('max_weight', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum total weight in grams', max_digits=8, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('min_volume', models.DecimalField(blank=True, decimal_places=4, help_text='Minimum total volume in cubic centimeters', max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.0001'))])),
                ('max_volume', models.DecimalField(blank=True, decimal_places=4, help_text='Maximum total volume in cubic centimeters', max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.0001'))])),
                ('min_items', models.PositiveIntegerField(blank=True, help_text='Minimum number of items', null=True)),
                ('max_items', models.PositiveIntegerField(blank=True, help_text='Maximum number of items', null=True)),
                ('force_mailer', models.BooleanField(default=False, help_text='Force use of mailer for matching items')),
                ('force_separate_packaging', models.BooleanField(default=False, help_text='Force items to be packaged separately')),
                ('additional_cost', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Additional packing cost in USD', max_digits=6, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('cost_multiplier', models.DecimalField(decimal_places=2, default=Decimal('1.00'), help_text='Cost multiplier for matching items', max_digits=4, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('preferred_box', models.ForeignKey(blank=True, help_text='Preferred box for items matching this rule', null=True, on_delete=django.db.models.deletion.CASCADE, to='shipping.box')),
                ('product_types', models.ManyToManyField(blank=True, help_text='Apply rule only to these product types', to='products.producttype')),
            ],
            options={
                'verbose_name': 'Packing Rule',
                'verbose_name_plural': 'Packing Rules',
                'ordering': ['-priority', 'title'],
            },
        ),
        migrations.CreateModel(
            name='CarrierService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_name', models.CharField(help_text="Service name (e.g., 'Standard Parcel')", max_length=100)),
                ('service_code', models.CharField(help_text='Service code for API calls', max_length=50)),
                ('is_active', models.BooleanField(default=True, help_text='Is this service available?')),
                ('estimated_days', models.PositiveIntegerField(default=3, help_text='Estimated delivery days')),
                ('max_days', models.PositiveIntegerField(default=7, help_text='Maximum delivery days')),
                ('cost_multiplier', models.DecimalField(decimal_places=2, default=Decimal('1.00'), help_text='Cost multiplier for this service', max_digits=4, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('supports_insurance', models.BooleanField(default=False, help_text='Does this service support insurance?')),
                ('supports_signature', models.BooleanField(default=False, help_text='Does this service support signature confirmation?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('carrier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='services', to='shipping.carrier')),
            ],
            options={
                'verbose_name': 'Carrier Service',
                'verbose_name_plural': 'Carrier Services',
                'ordering': ['carrier__title', 'estimated_days', 'service_name'],
                'unique_together': {('carrier', 'service_code')},
            },
        ),
    ]
