from django.db.models import Prefetch
from rest_framework.viewsets import ModelViewSet
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import serializers
from django.db import IntegrityError
from .models import Wishlist
from .serializers import WishlistSerializer
from apps.products.models import Product, ProductVariant
from utils.permissions import IsAdminOrCustomer
from utils.pagination import DefaultPagination


# Serializer to validate incoming request data
class ToggleProductSerializer(serializers.Serializer):
    product_id = serializers.IntegerField(required=True)


class WishlistViewSet(ModelViewSet):
    serializer_class = WishlistSerializer
    permission_classes = [IsAdminOrCustomer]
    pagination_class = DefaultPagination

    # Override to filter wishlists by the logged-in customer
    def get_queryset(self):
        # Check if user is authenticated first
        if not self.request.user.is_authenticated:
            return Wishlist.objects.none()  # Return empty queryset for unauthenticated users
        # Prefetch customer, product, and nested relations
        queryset = Wishlist.objects.select_related(
            'customer__user',
            'product'
        ).prefetch_related(
            Prefetch(
                'product__product_variant',  # Fetch variants
                queryset=ProductVariant.objects.prefetch_related('product_image')
            )
        )

        try:
            return queryset.filter(customer=self.request.user.customer)
        except AttributeError:
            # Handle case where request.user.customer doesn’t exist
            return Wishlist.objects.none()

    # Custom action for toggling a product in the wishlist
    @action(detail=False, methods=['post'])
    def toggle_product(self, request):
        # Check if user is authenticated
        if not request.user.is_authenticated:
            return Response({'error': 'Authentication required'}, status=401)
        # Validate the incoming request data using a serializer
        serializer = ToggleProductSerializer(data=request.data)
        if not serializer.is_valid():
            # Return validation errors if data is invalid
            return Response(serializer.errors, status=400)

        product_id = serializer.validated_data['product_id']

        try:
            # Check if the product exists in the database
            product = Product.objects.get(id=product_id)
        except Product.DoesNotExist:
            # Respond with 404 if the product is not found
            return Response({'error': 'Product not found'}, status=404)

        # Check if the user has a customer profile
        try:
            customer = request.user.customer
        except AttributeError:
            return Response({'error': 'Customer profile not found'}, status=400)

        # Check if the product is already in the customer's wishlist
        wishlist_item = Wishlist.objects.filter(
            customer=customer,
            product=product
        ).first()

        if wishlist_item:
            # If the product is found, remove it from the wishlist
            wishlist_item.delete()
            return Response({'status': 'Product removed from wishlist'})

        try:
            # If not found, add the product to the wishlist
            Wishlist.objects.create(
                customer=customer,  # Use the customer variable we defined earlier
                product=product
            )
            return Response({'status': 'Product added to wishlist'})
        except IntegrityError:
            # Handle uniqueness constraint violation
            return Response({'error': 'Product already in wishlist'}, status=400)
        except Exception as e:
            # Catch any unexpected errors
            return Response({'error': str(e)}, status=500)
